# EmonLibDB

The emonLibDB library here is mirrored from <PERSON>'s release thread on the OpenEnergyMonitor forums here [OpenEnergyMonitor Forum: EmonLibDB](https://community.openenergymonitor.org/t/emonlibdb-version-1-0-0/23535).

---

EmonLibDB is a Continuous Monitoring Arduino library designed for AVR-DB based electricity monitoring hardware such as the EmonTx4.

It is able to monitor continuously 3 voltage channels and 12 current/power/energy channels, three pulse inputs (or two pulse inputs and one low voltage analogue input).

It has has not been possible to include temperature measurement. Temperature measurement is available on the single phase emonLibCM based EmonTx4 firmware, see forum post here for more details [EmonTx4 DS18B20 Temperature sensing & firmware release 1.5.7](https://community.openenergymonitor.org/t/emontx4-ds18b20-temperature-sensing-firmware-release-1-5-7/23496).

Like emonLibCM, emonLibDB will always give an accurate measurement of the average over the reporting period of the voltage, and for each current/power channel the current, real and apparent power and power factor. A cumulative total of Watt-hours for each channel is also available. It is suitable for single phase, three phase or split phase operation at 50 Hz or 60 Hz. it is also possible to monitor current and real power/active energy in one or more arms of a load wired in delta on a 3-phase 240/415 V system, or 240 V loads in the North American split-phase system.

When used with the emonVs, a combined monitor and power supply, no adjustment is necessary for the system voltage, the input voltage range is 85 V -- 254 V, and only minor adjustments will be needed to trim the calibration. The currrent/power inputs can be calibrated for any realistic current, the calibration value being the nominal rating of the current transformer.

## INSTALLING THE LIBRARY

If you prefer installation using zip files please see Robert Wall's original post here [OpenEnergyMonitor Forum: EmonLibDB](https://community.openenergymonitor.org/t/emonlibdb-version-1-0-0/23535)

### Arduino

To install using git, navigate to your Arduino libraries directory and install using:

    git clone https://github.com/openenergymonitor/emonLibDB


## USING THE LIBRARY

Three example sketches are provided as part of the distribution:

- **EmonTxV4DB_min** is the absolute minimum sketch required to exercise the library and produce meaningful values.
- **EmonTxV4DB_max** gives an example of every API call available. However, as distributed, it actually changes nothing as everything is again given the default value. If you need to change one of the defaults, then only the API call that sets that value is needed, and you can copy and add that call to the "minimum' demo sketch.
- **EmonTxV4DB_rf** specifically illustrates using two data packets and two NodeIDs to send the complete set a data generated by 12 channels, using the Low Power Lab library.

The EmonLibDB library is not a direct replacement for the modified versions of emonLibCM for the emonTx4.

## [User Guide](guide.md)

## Acknowledgements

Jörg Becker (@joergbecker32) for his background work on interrupts and the ADC.<br>
Robin Emley (@calypso_rae) for his energy diverter software, from which the major part of the library was derived by @TrystanLea<br>
@ursi (Andries) and @mafheldt (Mike Afheldt) for suggestions made at https://community.openenergymonitor.org/t/emonlib-inaccurate-power-factor/3790 and https://community.openenergymonitor.org/t/rms-calculations-in-emonlib-and-learn-documentation/3749/3<br>
Angus Logan (@awjlogan) for his suggestions regarding memory use<br>
Dr.C B Markwardt (@cbmarkwardt)for his suggestion to use the AVR Hardware Multiplier, and to @dBC for reminding me.<br>

## Support

Please see [emonLibDB (Support)](https://community.openenergymonitor.org/t/emonlibdb-support/23536) to comment or request support.
