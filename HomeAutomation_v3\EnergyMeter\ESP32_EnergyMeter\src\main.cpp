#include <Arduino.h>
#include <WiFi.h>
#include <Wire.h>
#include <ACAN_ESP32.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <ArduinoHA.h>
#include "global_config.h"
#include "eventManager.h"
#include "deviceSettings.h"
#include "WebServer.h"
#include "ioModule.h"
#include "statusLed.h"


EthernetClient mqqtClient;

WebServer webServer(80);

HADevice HA_device;
HAMqtt mqtt(mqqtClient, HA_device, 70);

char *mqtt_username;
char *mqtt_password;
bool mqtt_connected = false;

HASensorNumber haVoltage1("Voltage1", HABaseDeviceType::PrecisionP2);
HASensorNumber haVoltage2("Voltage2", HABaseDeviceType::PrecisionP2);
HASensorNumber haVoltage3("Voltage3", HABaseDeviceType::PrecisionP2);

HASensorNumber haCurrent1("Current1", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent2("Current2", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent3("Current3", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent4("Current4", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent5("Current5", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent6("Current6", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent7("Current7", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent8("Current8", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent9("Current9", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent10("Current10", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent11("Current11", HABaseDeviceType::PrecisionP2);
HASensorNumber haCurrent12("Current12", HABaseDeviceType::PrecisionP2);

HASensorNumber haRealPower1("RealPower1");
HASensorNumber haRealPower2("RealPower2");
HASensorNumber haRealPower3("RealPower3");
HASensorNumber haRealPower4("RealPower4");
HASensorNumber haRealPower5("RealPower5");
HASensorNumber haRealPower6("RealPower6");
HASensorNumber haRealPower7("RealPower7");
HASensorNumber haRealPower8("RealPower8");
HASensorNumber haRealPower9("RealPower9");
HASensorNumber haRealPower10("RealPower10");
HASensorNumber haRealPower11("RealPower11");
HASensorNumber haRealPower12("RealPower12");

HASensorNumber haApparentPower1("ApparentPower1");
HASensorNumber haApparentPower2("ApparentPower2");
HASensorNumber haApparentPower3("ApparentPower3");
HASensorNumber haApparentPower4("ApparentPower4");
HASensorNumber haApparentPower5("ApparentPower5");
HASensorNumber haApparentPower6("ApparentPower6");
HASensorNumber haApparentPower7("ApparentPower7");
HASensorNumber haApparentPower8("ApparentPower8");
HASensorNumber haApparentPower9("ApparentPower9");
HASensorNumber haApparentPower10("ApparentPower10");
HASensorNumber haApparentPower11("ApparentPower11");
HASensorNumber haApparentPower12("ApparentPower12");

HASensorNumber haPowerFactor1("PowerFactor1", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor2("PowerFactor2", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor3("PowerFactor3", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor4("PowerFactor4", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor5("PowerFactor5", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor6("PowerFactor6", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor7("PowerFactor7", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor8("PowerFactor8", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor9("PowerFactor9", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor10("PowerFactor10", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor11("PowerFactor11", HABaseDeviceType::PrecisionP2);
HASensorNumber haPowerFactor12("PowerFactor12", HABaseDeviceType::PrecisionP2);

HASensorNumber haEnergy1("Energy1");
HASensorNumber haEnergy2("Energy2");
HASensorNumber haEnergy3("Energy3");
HASensorNumber haEnergy4("Energy4");
HASensorNumber haEnergy5("Energy5");
HASensorNumber haEnergy6("Energy6");
HASensorNumber haEnergy7("Energy7");
HASensorNumber haEnergy8("Energy8");
HASensorNumber haEnergy9("Energy9");
HASensorNumber haEnergy10("Energy10");
HASensorNumber haEnergy11("Energy11");
HASensorNumber haEnergy12("Energy12");

HASensorNumber haTemperatureSensor("Temperature", HASensorNumber::PrecisionP2);

HASensorNumber* haCurrentSensors[] = {&haCurrent1, &haCurrent2, &haCurrent3, &haCurrent4, &haCurrent5, &haCurrent6, &haCurrent7, &haCurrent8, &haCurrent9, &haCurrent10, &haCurrent11, &haCurrent12};
HASensorNumber* haRealPowerSensors[] = {&haRealPower1, &haRealPower2, &haRealPower3, &haRealPower4, &haRealPower5, &haRealPower6, &haRealPower7, &haRealPower8, &haRealPower9, &haRealPower10, &haRealPower11, &haRealPower12};
HASensorNumber* haApparentPowerSensors[] = {&haApparentPower1, &haApparentPower2, &haApparentPower3, &haApparentPower4, &haApparentPower5, &haApparentPower6, &haApparentPower7, &haApparentPower8, &haApparentPower9, &haApparentPower10, &haApparentPower11, &haApparentPower12};
HASensorNumber* haPowerFactorSensors[] = {&haPowerFactor1, &haPowerFactor2, &haPowerFactor3, &haPowerFactor4, &haPowerFactor5, &haPowerFactor6, &haPowerFactor7, &haPowerFactor8, &haPowerFactor9, &haPowerFactor10, &haPowerFactor11, &haPowerFactor12};
HASensorNumber* haEnergySensors[] = {&haEnergy1, &haEnergy2, &haEnergy3, &haEnergy4, &haEnergy5, &haEnergy6, &haEnergy7, &haEnergy8, &haEnergy9, &haEnergy10, &haEnergy11, &haEnergy12};

OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature tempSensor(&oneWire);

// CANbus
static uint32_t gBlinkLedDate = 0;
static uint8_t gReceivedFrameCount = 0;
static uint32_t gSentFrameCount = 0;

//const int temperatureUpdateTimeout = 30000; //in ms
//const int temperatureUpdateReadyTimeout =  temperatureUpdateTimeout + 750; //in ms
//unsigned long previousTemperatureUpdate = 0;
//bool isTempConversionInProgress = false;

//Update hold buttons
const int buttonHoldUpdateTimeout = 100; //in ms
unsigned long previousButtonHoldUpdate = 0;
uint8_t buttonHoldEnable = 0;

//Tasks
TaskHandle_t tempTaskHandle = NULL;
TaskHandle_t inputsTaskHandle = NULL;
TaskHandle_t powerReportTaskHandle = NULL;

// Temperature reading task
void TempTask(void *pvParameters) {
  while (1) {
    tempSensor.requestTemperatures();  // Start conversion (takes ~750ms)

      // Continue doing other things while waiting
      vTaskDelay(pdMS_TO_TICKS(750));  // Wait for the conversion (750ms)

      // Read temperature
      WebServer::temperature = tempSensor.getTempCByIndex(0);

      #ifdef DEBUG_TO_UART
        Serial.println("Temperature: " + String(WebServer::temperature) + " °C");
      #endif

      if(WebServer::temperature >= MAX_TEMPERATURE) {
        if(DeviceSettings::getInstance().getEventLogEnabled()) {
          EventManager::getInstance().logEvent(EventManager::EventType::OVERHEAT, (uint8_t) (WebServer::temperature));
        }
      }
  
      if (DeviceSettings::getInstance().getMQTTEnabled())
      {
        haTemperatureSensor.setValue(WebServer::temperature);
      }
  

      vTaskDelay(pdMS_TO_TICKS(10000));  // Wait before next reading
  }
}

void PowerReportTask(void* parameter) {
  /*
    while (1) {
        if(DeviceSettings::getInstance().isEnergyMonitoringEnabled()) {
            for(int i = 0; i < NUM_OUTPUTS; i++) {
                // Check if output is ON
                bool outputState = (IOModule::getInstance().outputPorts >> i) & 0x01;
                if(outputState) {
                    // Calculate energy consumption for the last minute
                    float powerWatts = DeviceSettings::getInstance().getOutputEnergyConsumption(i);

                    if(powerWatts <= 0.0f) {
                        continue;
                    }

                    // Convert Watts to Watt-hours for the 1-minute interval
                    float energyWh = powerWatts / 60.0f;  // Convert 1 minute of usage to Wh
                    
                    // Get current value and add new consumption
                    float currentValue = haEnergyDevices[i]->getCurrentValue().toFloat();
                    float newValue = currentValue + energyWh;
                    
                    haEnergyDevices[i]->setValue(newValue, true);
                }
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(60000));
    }
    */
}

void onMqttStateChanged(HAMqtt::ConnectionState state) {
    int8_t int_state = static_cast<int8_t>(state);

    switch (int_state)
    {
      case -5:
        Serial.println(F("MQTT state changed to: StateConnecting"));
        mqtt_connected = false;
        break;
      case -4:
        Serial.println(F("MQTT state changed to: StateConnectionTimeout"));
        mqtt_connected = false;
        break;
      case -3:
        Serial.println(F("MQTT state changed to: StateConnectionLost"));
        mqtt_connected = false;
        break;
      case -2:
        Serial.println(F("MQTT state changed to: StateConnectionFailed"));
        mqtt_connected = false;
        break;
      case -1:
        Serial.println(F("MQTT state changed to: StateDisconnected"));
        mqtt_connected = false;
        break;
      case 0:
        Serial.println(F("MQTT state changed to: StateConnected"));
        mqtt_connected = true;
        break;
      case 1:
        Serial.println(F("MQTT state changed to: StateBadProtocol"));
        mqtt_connected = false;
        break;
      case 2:
        Serial.println(F("MQTT state changed to: StateBadClientId"));
        mqtt_connected = false;
        break;
      case 3:
        Serial.println(F("MQTT state changed to: StateUnavailable"));
        mqtt_connected = false;
        break;
      case 4:
        Serial.println(F("MQTT state changed to: StateBadCredentials"));
        mqtt_connected = false;
        break;
      case 5:
        Serial.println(F("MQTT state changed to: StateUnauthorized"));
        mqtt_connected = false;
        break;
      default:
        break;
    }

    Serial.println(static_cast<int8_t>(state));
}

void onStateCommand(bool state, HALight* sender) {
    Serial.print("State: ");
    Serial.println(state);
/*
    for(int i = 0; i < NUM_OUTPUTS; i++) {
        if(haLightDevices[i] == sender) {
            IOModule::getInstance().setOutput(i, (state == true?1:0));
            EventManager::getInstance().logEvent(EventManager::EventType::MQTT_STATE, i, (state == true)? 1 : 0);
            break;
        }
    }
*/
    sender->setState(state); // report state back to the Home Assistant
}

void initHADevices() {

#ifdef DEBUG_TO_UART
  Serial.println("INIT - HA Devices");
#endif

  const byte *deviceId = DeviceSettings::getInstance().getMQTTDeviceID();

  HA_device.setUniqueId(deviceId, 6);
  HA_device.setSoftwareVersion(TOSTRING(APP_SOFTWARE_VERSION));
  HA_device.setName("PowerMeter - 12ch");
  HA_device.setManufacturer("IceSoft");
  HA_device.setModel("DIN - 12ch PowerMeter");
  HA_device.setConfigurationUrl(webServer.localURL);
  
  // This method enables availability for all device types registered on the device.
  // For example, if you have 5 sensors on the same device, you can enable
  // shared availability and change availability state of all sensors using
  // single method call "device.setAvailability(false|true)"
  //HA_device.enableSharedAvailability();

  // Optionally, you can enable MQTT LWT feature. If device will lose connection
  // to the broker, all device types related to it will be marked as offline in
  // the Home Assistant Panel.
  HA_device.enableLastWill();

  // The unique ID of each device type will be prefixed with the device's ID once enabled.
  HA_device.enableExtendedUniqueIds();


  haVoltage1.setName("Voltage");
  haVoltage1.setDeviceClass("voltage");
  haVoltage1.setUnitOfMeasurement("V");
  haVoltage1.setCurrentValue(0.0f);

  haVoltage2.setName("Voltage");
  haVoltage2.setDeviceClass("voltage");
  haVoltage2.setUnitOfMeasurement("V");
  haVoltage2.setCurrentValue(0.0f);

  haVoltage3.setName("Voltage");
  haVoltage3.setDeviceClass("voltage");
  haVoltage3.setUnitOfMeasurement("V");
  haVoltage3.setCurrentValue(0.0f);


  for(int i = 0; i < NUM_INPUTS; i++) {
    //in order to set custom name
    //you must use global variable, because thats the way MQTT lib is referenceing them
    //check the user and password copy below

    haCurrentSensors[i]->setName(haCurrentSensors[i]->uniqueId());
    haCurrentSensors[i]->setDeviceClass("current");
    haCurrentSensors[i]->setUnitOfMeasurement("A");
    haCurrentSensors[i]->setCurrentValue(0.0f);

    haRealPowerSensors[i]->setName("Power");
    haRealPowerSensors[i]->setDeviceClass("voltage");
    haRealPowerSensors[i]->setUnitOfMeasurement("W");
    haRealPowerSensors[i]->setCurrentValue((int16_t)0);

    haApparentPowerSensors[i]->setName(haApparentPowerSensors[i]->uniqueId());
    haApparentPowerSensors[i]->setDeviceClass("apparent_power");
    haApparentPowerSensors[i]->setUnitOfMeasurement("VA");
    haApparentPowerSensors[i]->setCurrentValue((int16_t)0);

    haPowerFactorSensors[i]->setName(haPowerFactorSensors[i]->uniqueId());
    haPowerFactorSensors[i]->setDeviceClass("power_factor");
    haPowerFactorSensors[i]->setUnitOfMeasurement("%");
    haPowerFactorSensors[i]->setCurrentValue(0.0f);

    haEnergySensors[i]->setName(haEnergySensors[i]->uniqueId());
    haEnergySensors[i]->setDeviceClass("energy");
    haEnergySensors[i]->setUnitOfMeasurement("kW/h");
    haEnergySensors[i]->setCurrentValue((int32_t)0);
  }

  haTemperatureSensor.setIcon("mdi:temperature-celsius");
  haTemperatureSensor.setName("Temperature");
  haTemperatureSensor.setUnitOfMeasurement("C");
  
  mqtt.onStateChanged(onMqttStateChanged);
  
  //Copy the username to a global variable. 
  //The mqtt lib will not work otherway     
  int strLen = DeviceSettings::getInstance().getMQTTUsername().length() + 1; // +1 for the null terminator
  mqtt_username = new char[strLen];
  DeviceSettings::getInstance().getMQTTUsername().toCharArray(mqtt_username, strLen);
  
  strLen = DeviceSettings::getInstance().getMQTTPassword().length() + 1; // +1 for the null terminator
  mqtt_password = new char[strLen];
  DeviceSettings::getInstance().getMQTTPassword().toCharArray(mqtt_password, strLen);

  mqtt.begin(DeviceSettings::getInstance().getMQTTIPAddress(), mqtt_username, mqtt_password);
}


void setup()
{
  // Init pins
  pinMode(LAN_RESET, OUTPUT);
  pinMode(LAN_INTERRUPT, INPUT);
  pinMode(STATUS_LED, OUTPUT);
  pinMode(PIC_INTERRUPT_PIN, INPUT);
  pinMode(PIC_MCLR, INPUT);
  pinMode(PIC_PGD, INPUT);
  pinMode(PIC_PGC, INPUT);

  digitalWrite(LAN_RESET, LOW);
  
  StatusLed::begin(STATUS_LED); 

  Serial.begin(115200);

  // Disable WiFi
  WiFi.disconnect(true);
  WiFi.mode(WIFI_OFF);

  DeviceSettings::getInstance().begin();
  //DeviceSettings::getInstance().resetPreferences();

  // Initialize pic IO module
  IOModule::getInstance().begin(PIC_RX_PIN, PIC_TX_PIN, PIC_INTERRUPT_PIN);
  //IOModule::getInstance().setDefaults();
  //IOModule::getInstance().readInputs();
  //IOModule::getInstance().readOutputs();

  //Serial.println("Starting setup");
  IOModule::getInstance().setVoltageInput(0, 100.0, 0.16);
  IOModule::getInstance().setVoltageInput(1, 200.0, 0.16);
  IOModule::getInstance().setVoltageInput(2, 300.0, 0.16);


  //Serial.println("Voltage done;");

  //IOModule::getInstance().setCurrentInput(0, 10.0, 0.3);
//  IOModule::getInstance().setCurrentInput(1, 10.0, 0.3);
  //IOModule::getInstance().setCurrentInput(2, 10.0, 0.3);

  //Serial.println("Current done;");

  //IOModule::getInstance().setPowerInput(0, 0);
//  IOModule::getInstance().setPowerInput(1, 0);
//  IOModule::getInstance().setPowerInput(2, 0);
  //IOModule::getInstance().setPowerInput(3, 0);

  //Serial.println("Power done;");


  //IOModule::getInstance().startMeasurement();
  Serial.println("Init EmonLib done;");



  tempSensor.begin();

  // Wait
  delay(50);

  // Start LAN
  digitalWrite(LAN_RESET, HIGH);

  webServer.begin();


  // Init HA Devices
  if (DeviceSettings::getInstance().getMQTTEnabled())
  {
    initHADevices();
  }

  DeviceAddress tempDeviceAddress;
  tempSensor.getAddress(tempDeviceAddress, 0);
  tempSensor.setResolution(tempDeviceAddress, 10);

  // Create temperature task
  xTaskCreatePinnedToCore(TempTask, "TempTask", 2048, NULL, 1, &tempTaskHandle, 1);  // Run on Core 1 with priotity 1 (lowest)

  // Create power report task
  /*
  if(DeviceSettings::getInstance().isEnergyMonitoringEnabled()) {
    xTaskCreatePinnedToCore(
        PowerReportTask,
        "PowerReport",
        2048,
        NULL,
        1,  // Low priority
        &powerReportTaskHandle,
        1   // Run on Core 1
    );
  }
  */
  if(DeviceSettings::getInstance().getEventLogEnabled()) {
    EventManager::getInstance().begin();
  }

}


void loop()
{
  webServer.loop();

  if (DeviceSettings::getInstance().getMQTTEnabled())
  {
    mqtt.loop();
  }

  //if(digitalRead(PIC_INTERRUPT_PIN) == HIGH) {
    delay(10);

    IOModule::getInstance().readVoltage();
        
    Serial.print("Voltage 0: ");
    Serial.println(IOModule::emonVoltage[0]);
    Serial.print("Voltage 1: ");
    Serial.println(IOModule::emonVoltage[1]);
    Serial.print("Voltage 2: ");
    Serial.println(IOModule::emonVoltage[2]);
    Serial.print("Frequency: ");
    Serial.println(IOModule::emonFrequency);
    
    Serial.println("--------------------------------");

/*

    IOModule::getInstance().readPower(0);
    Serial.print("Power 0: ");
    Serial.println(IOModule::emonRealPower[0]);
    Serial.print("Current 0: ");
    Serial.println(IOModule::emonCurrent[0]);
    Serial.print("Voltage 0: ");
    Serial.println(IOModule::emonVoltage[0]);
    Serial.print("Power Factor 0: ");
    Serial.println(IOModule::emonPowerFactor[0]);
    Serial.println("--------------------------------");
    IOModule::getInstance().readPower(1);
    Serial.print("Power 1: ");
    Serial.println(IOModule::emonRealPower[1]);
    Serial.print("Current 1: ");
    Serial.println(IOModule::emonCurrent[1]);
    Serial.print("Voltage 1: ");
    Serial.println(IOModule::emonVoltage[1]);
    Serial.print("Power Factor 1: ");
    Serial.println(IOModule::emonPowerFactor[1]);
    Serial.println("--------------------------------");
    IOModule::getInstance().readPower(2);
    Serial.print("Power 2: ");
    Serial.println(IOModule::emonRealPower[2]);
    Serial.print("Current 2: ");
    Serial.println(IOModule::emonCurrent[2]);
    Serial.print("Voltage 2: ");
    Serial.println(IOModule::emonVoltage[2]);
    Serial.print("Power Factor 2: ");
    Serial.println(IOModule::emonPowerFactor[2]);
    Serial.println("--------------------------------");
    */
    delay(5000);
  //}

  StatusLed::update(); 

  delay(1);
}
