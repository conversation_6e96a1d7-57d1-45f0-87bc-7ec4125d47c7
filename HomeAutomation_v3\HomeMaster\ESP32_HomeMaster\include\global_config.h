#ifndef CONFIG_H
#define CONFIG_H

#define APP_SOFTWARE_VERSION 2

#define DEBUG_TO_UART 1

#define NUM_INPUTS   8
#define NUM_OUTPUTS 9

#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)

#define MAX_TEMPERATURE 70

#define LAN_RESET 13
#define LAN_INTERRUPT 4
//LAN_CS is used in the Ethernet lib by the name PIN_SPI_SS_ETHERNET_LIB
#define PIN_SPI_SS_ETHERNET_LIB 5

#define IR_KITCHEN           35 //Kitchen
#define IR_LIVING            34 //Living
#define DHT_KITCHEN          26
#define DHT_LIVING           27

//#define CC1101_CS          22
//#define CC1101_MISO        19
//#define CC1101_MOSI        23
//#define CC1101_CLK         18

//#define CC1101_GDO0        12
//#define CC1101_GDO2        15

#define PIC_RX_PIN 16
#define PIC_TX_PIN 17
#define PIC_INTERRUPT_PIN 25

#define STATUS_LED 22

#define ONE_WIRE_BUS 21



#define MONITOR_SERIAL Serial
#define RADAR_SERIAL Serial1
#define RADAR_RX_PIN 32
#define RADAR_TX_PIN 33
#define RADAR_INTERRUPT 14

#endif