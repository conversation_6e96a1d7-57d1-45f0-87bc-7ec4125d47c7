#include "ioModule.h"

#define START_BYTE  0xAA
#define ACK_BYTE    '#'
#define ERROR_BYTE  '@'
#define TIMEOUT_MS  1000

#define BUFFER_SIZE 64

// Define static member variables
float IOModule::emonVoltage[3] = {0.0, 0.0, 0.0};
float IOModule::emonFrequency = 0.0;
float IOModule::emonCurrent[NUM_INPUTS] = {0.0};
int16_t IOModule::emonRealPower[NUM_INPUTS] = {0};
int16_t IOModule::emonApparentPower[NUM_INPUTS] = {0};
float IOModule::emonPowerFactor[NUM_INPUTS] = {0.0};
int32_t IOModule::emonEnergy[NUM_INPUTS] = {0};

// Calculate CRC using XOR method
uint8_t calculateCRC(const uint8_t* data, uint8_t length) {
    uint8_t crc = 0;
    for (uint8_t i = 0; i < length; i++) {
        crc ^= data[i];
    }
    return crc;
}

IOModule::IOModule() {

}

IOModule& IOModule::getInstance() {
  static IOModule instance;
  return instance;
}

void IOModule::begin(uint8_t RX_PIN, uint8_t TX_PIN, uint8_t INTERRUPT_PIN)
{
    Serial2.setPins(RX_PIN, TX_PIN);
    Serial2.begin(115200);

    _interrupt_pin = INTERRUPT_PIN;

    readVersion();

    Serial.print("Atmel Init Done. Version : ");
    Serial.println(picVersion);
}

void IOModule::sendResponse(uint8_t cmd, const uint8_t* payload, uint8_t payloadLen) {
    // Prepare complete packet in buffer
    uint8_t packet[BUFFER_SIZE];
    uint8_t packetLen = 0;
    
    // Add header
    packet[packetLen++] = START_BYTE;
    packet[packetLen++] = payloadLen + 2;  // +2 for command and CRC
    packet[packetLen++] = cmd;
    
    // Add payload if any
    if (payloadLen > 0) {
        memcpy(&packet[packetLen], payload, payloadLen);
        packetLen += payloadLen;
    }
    
    // Calculate CRC for entire packet except CRC byte
    uint8_t crc = calculateCRC(packet, packetLen);
    packet[packetLen++] = crc;
    
    // Debug output
    Serial.print("TX: ");
    for(uint8_t i = 0; i < packetLen; i++) {
        Serial.print(packet[i], HEX);
        Serial.print(" ");
    }
    Serial.println();
    
    // Send complete packet
    Serial2.write(packet, packetLen);
}

bool IOModule::readResponse() {
    uint8_t buffer[BUFFER_SIZE];
    uint8_t bufferIndex = 0;
    unsigned long startTime = millis();
    bool startFound = false;
    uint8_t expectedLen = 0;
    
    while (millis() - startTime < TIMEOUT_MS) {
        if (Serial2.available() > 0) {
            uint8_t byte = Serial2.read();
            
            if (!startFound) {
                if (byte == START_BYTE) {
                    startFound = true;
                    bufferIndex = 0;
                    buffer[bufferIndex++] = byte;
                    Serial.println("RX: Start byte found");
                }
                continue;
            }

            buffer[bufferIndex++] = byte;
            
            if (bufferIndex == 2) {  // Just read length byte (after start and command)
                expectedLen = byte + 2; // +2 for command and CRC
                Serial.print("RX: Expected length: ");
                Serial.println(expectedLen);
            }
            
            
            // Check if we have received the complete message
            if (startFound && bufferIndex == expectedLen) {
                // Debug output
                Serial.print("RX: ");
                for(uint8_t i = 0; i < bufferIndex; i++) {
                    Serial.print(buffer[i], HEX);
                    Serial.print(" ");
                }
                Serial.println();
                
                // Verify CRC
                uint8_t receivedCRC = buffer[expectedLen - 1];
                uint8_t calculatedCRC = calculateCRC(buffer, expectedLen - 1);
                
                if (receivedCRC != calculatedCRC) {
                    Serial.print("RX: CRC Error - Received: ");
                    Serial.print(receivedCRC, HEX);
                    Serial.print(" Calculated: ");
                    Serial.println(calculatedCRC, HEX);
                    return false;
                }
                
                Serial.println("RX: CRC OK");
                
                // Copy payload to global buffer
                globalPayloadLen = expectedLen - 1;  // Subtract CRC
                memcpy(globalPayload, &buffer[3], globalPayloadLen);
                
                return true;
            }
        }
    }
    
    Serial.println("RX: Timeout");
    return false;  // Timeout
}

bool IOModule::waitForAck() {
    uint8_t buffer[BUFFER_SIZE];
    uint8_t bufferIndex = 0;
    unsigned long startTime = millis();
    bool startFound = false;
    uint8_t expectedLen = 0;
    
    while (millis() - startTime < TIMEOUT_MS) {
        if (Serial2.available() > 0) {
            uint8_t byte = Serial2.read();
            if(byte == ACK_BYTE) {
                Serial.println("ACK");
                return true;
            } else {
                Serial.println("Error");
                return false;
            }
        }
    }
    
    Serial.println("ACK: Timeout");
    return false;  // Timeout
}

uint8_t IOModule::readStatus() {
    uint8_t status = 0;

    sendResponse(0x06, NULL, 0);
    if(readResponse()) {
        memcpy(&status, globalPayload, 1);    
    }
    return status;
}

bool IOModule::readVoltage() {
    sendResponse(0x07, NULL, 0);
    if(readResponse()) {
        float v1, v2, v3, frequency;
        memcpy(&v1, globalPayload, 4);
        memcpy(&v2, globalPayload + 4, 4);
        memcpy(&v3, globalPayload + 8, 4);
        //memcpy(&frequency, globalPayload + 12, 4);

        IOModule::emonVoltage[0] = v1;
        IOModule::emonVoltage[1] = v2;
        IOModule::emonVoltage[2] = v3;
        //IOModule::emonFrequency = frequency;

        return true;
    }
    return false;
}

bool IOModule::readPower(int channel) {
    uint8_t payload[1];
    payload[0] = channel;

    sendResponse(0x08, payload, 1);
    if(readResponse()) {
        float irms;
        int16_t realPower;
        int16_t apparentPower;
        int32_t wattHour;
        float pf;

        memcpy(&irms, globalPayload, 4);
        memcpy(&realPower, globalPayload + 4, 2);
        memcpy(&apparentPower, globalPayload + 6, 2);
        memcpy(&wattHour, globalPayload + 8, 4);
        memcpy(&pf, globalPayload + 12, 4);

        IOModule::emonCurrent[channel] = irms;
        IOModule::emonRealPower[channel] = realPower;
        IOModule::emonApparentPower[channel] = apparentPower;
        IOModule::emonEnergy[channel] = wattHour;
        IOModule::emonPowerFactor[channel] = pf;

        return true;
    }

    return false;
}

bool IOModule::setVoltageInput(int channel, double voltage, double phase) {
    uint8_t payloadRequest[16];
   
    memcpy(&payloadRequest[0], &channel, 1);
    memcpy(&payloadRequest[1], &voltage, 4);
    memcpy(&payloadRequest[5], &phase, 4);

    sendResponse(0x02, payloadRequest, 9);  

    return waitForAck();
}

bool IOModule::setCurrentInput(int channel, double current, double phase) {
    uint8_t payloadRequest[16];
   
    memcpy(&payloadRequest[0], &channel, 1);
    memcpy(&payloadRequest[1], &current, 4);
    memcpy(&payloadRequest[5], &phase, 4);

    sendResponse(0x03, payloadRequest, 9);  

    return waitForAck();
}

bool IOModule::setPowerInput(int currentChannel, int voltageChannel) {
    uint8_t payloadRequest[2];
   
    payloadRequest[0] = currentChannel;
    payloadRequest[1] = voltageChannel;

    sendResponse(0x04, payloadRequest, 2);  

    return waitForAck();
}

bool IOModule::setDatalogPeriod(float period) {
    uint8_t payloadRequest[4];
   
    memcpy(&payloadRequest[0], &period, 4);

    sendResponse(0x01, payloadRequest, 4);  

    return waitForAck();
}

bool IOModule::startMeasurement() {
    sendResponse(0x05, NULL, 0);  
    return waitForAck();
}

bool IOModule::readVersion() {
    sendResponse(0x09, NULL, 0);
    if(readResponse()) {
        memcpy(&picVersion, globalPayload, 1);    
        return true;
    }
    return false;
}

bool IOModule::debug() {
    sendResponse(0x10, NULL, 0);
    return waitForAck();
}

bool IOModule::reboot() {
    sendResponse(0x1A, NULL, 0);
    return waitForAck();
}
