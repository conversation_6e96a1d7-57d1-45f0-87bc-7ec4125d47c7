#ifndef IOModule_lib
#define IOModule_lib

#include <Arduino.h>
#include "global_config.h"
#include "deviceSettings.h"

#define START_BYTE  0xAA
#define ACK_BYTE    '#'
#define ERROR_BYTE  '@'


class IOModule
{
private:
    static IOModule* instance;
    // Private constructor to enforce singleton pattern
    IOModule();

    //Used to determin the hold down on input buttons
    
    uint8_t globalPayload[32]; // Global payload buffer
    uint8_t globalPayloadLen = 0; // Length of received payload

    uint8_t _interrupt_pin;

    void sendResponse(uint8_t cmd, const uint8_t* payload, uint8_t payloadLen);
    bool readResponse();
    bool waitForAck();

public:
    // Singleton instance
    static IOModule& getInstance();

    static float emonVoltage[3];
    static float emonFrequency;
    static float emonCurrent[NUM_INPUTS];
    static int16_t emonRealPower[NUM_INPUTS];
    static int16_t emonApparentPower[NUM_INPUTS];
    static float emonPowerFactor[NUM_INPUTS];
    static int32_t emonEnergy[NUM_INPUTS];
    
    void begin(uint8_t RX_PIN, uint8_t TX_PIN, uint8_t INTERRUPT_PIN);
    
    uint8_t readStatus();
    bool readVoltage();
    bool readPower(int channel);

    bool setVoltageInput(int channel, double voltage, double phase);
    bool setCurrentInput(int channel, double current, double phase);
    bool setPowerInput(int currentChannel, int voltageChannel);
    bool setDatalogPeriod(float period);

    bool startMeasurement();

    bool readVersion();
    bool debug();
    bool reboot();

    uint8_t picVersion = 0;

};

#endif
