#ifndef STATUSLED_H
#define STATUSLED_H

#include <Arduino.h>

class StatusLed {
public:
    enum Pattern {
        BOOT,
        IDLE,
        INPUT_CHANGE,
        UPDATE,
    };

    static void begin(int ledPin);
    static void setPattern(Pattern pattern);
    static void update();
    static void toggle();

private:
    static int _ledPin;
    static Pattern _currentPattern;
    static unsigned long _previousMillis;
    static bool _ledState;
    static unsigned long _onTime;
    static unsigned long _offTime;
};

#endif
