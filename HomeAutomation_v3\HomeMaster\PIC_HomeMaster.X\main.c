#include "config.h"
#include <xc.h>
#include <string.h>
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

#define PIN_INTERRUPT LATBbits.LATB2
#define TMR1_ON T1CONbits.TMR1ON

#define START_BYTE  '!'
#define END_BYTE    ';'
#define OK_BYTE     '#'
#define ERROR_BYTE  '@'
#define ERROR_COMMAND  '^'
#define UART_BUFFER_SIZE 8
#define NUM_INPUTS 12
#define NUM_OUTPUTS 9

char APP_VERSION  = 1;

volatile char rxBuffer[UART_BUFFER_SIZE];
volatile char rxBufferSize = 0;
volatile char rxCommandReady = 0;


typedef struct {
    uint8_t state;              // debounced value 
    uint8_t pinState;           //value of the PORT

    uint8_t debouncing;         //1 = debouncing in progress
    uint8_t debouncedState;     //1 = waiting for debounce
    volatile uint16_t debounceCounter;    //Counter for debouncing
} InputPin;

volatile InputPin inputsPins[NUM_INPUTS] = {
    {0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0}
};

//Validated and debounced inputs
volatile uint8_t pinsToDebounce = 0;

//Time * 10 ms
uint16_t buttonDebounceTime = 10;



void Config(void) {

    //Xtal config @ 64mhz
    OSCCON  = 0b01110000; 
    OSCTUNEbits.PLLEN = 1;
    
    
    //Disable priority on interrupts
    RCONbits.IPEN = 0; 
    
    Config_IO();
    Config_UART();
    Config_Timer1();
    
    //Enable interrupts
    //Interrupts must be enabled last
    INTCONbits.GIE = 1; //Global Interrupt Enable bit
    INTCONbits.PEIE = 1; //Peripheral Interrupt Enable bit
    
    //Time to settle
    __delay_ms(10);
}

void Config_IO(void) {
    
    ANSELA = 0;
    ANSELB = 0;
    ANSELC = 0;
    ANSELD = 0;
    ANSELE = 0;
    
    TRISA = 0b00000000;
    TRISB = 0b11000000;
    TRISC = 0b10011110;
    TRISD = 0b00001111;
    TRISE = 0b000;
    
    LATA = 0;
    LATB = 0b00001000; //Turn off CC1101 on boot, because CC1101.GDO0 is connected to EPS32.gpio2. We need low or floating on that GPIO so we can enter flashing mode.
    LATC = 0;
    LATD = 0;
    LATE = 0;
}

void Config_UART(void) {
    
    // Configure EUSART1
    TX1STAbits.SYNC = 0; // Asynchronous mode
    TX1STAbits.BRGH = 1; // High Baud Rate Select bit
    BAUD1CONbits.BRG16 = 1; // 8-bit Baud Rate Generator

    SP1BRG = 138; // Set baud rate to 115200
    SP1BRGH = 0;   // High byte of SPBRG (only used if BRG16 = 1)

    RC1STAbits.SPEN = 1; // Enable EUSART1 (Serial Port Enable bit)
    TX1STAbits.TXEN = 1; // Enable transmitter
    RC1STAbits.CREN = 1; // Enable continuous receive

    // Configure the TX1 pin as an output
    TRISCbits.TRISC6 = 0; // RC6 as output (TX)
    TRISCbits.TRISC7 = 1; // RC7 as input (RX)
    
    //Enable interrupts
    PIE1bits.RC1IE = 1;
}

void Config_Timer1(void) {
    // Timer1 Registers:
    // Prescaler=1:8; TMR1 Preset=45536; Freq=0,10kHz; Period=10,00 ms
   
    TMR1H = 0xB1;     // preset for timer1 MSB register
    TMR1L = 0xE0;     // preset for timer1 LSB register       
    
    T1CONbits.T1CKPS1 = 1;// bits 5-4  Prescaler Rate Select bits
    T1CONbits.T1CKPS0 = 1;//
    //T1CONbits.T1OSCEN = 1;// bit 3 Timer1 Oscillator Enable Control: bit 1=on
    //T1CONbits.T1SYNC  = 1;// bit 2 Timer1 External Clock Input Synchronization Control bit: 1=Do not synchronize external clock input
    T1CONbits.TMR1CS  = 0;// bit 1 Timer1 Clock Source Select bit: 0=Internal clock (FOSC/4) / 1 = External clock from pin T13CKI (on the rising edge)
    
    T1GCONbits.TMR1GE = 0;
    
    PIR1bits.TMR1IF = 0;    // Clear Timer1 interrupt flag
    PIE1bits.TMR1IE = 1;    // Enable Timer1 interrupt
    
    TMR1_ON = 1;   // Turn on Timer1
}

void Check_IO() {
    
    uint16_t inputs = readInputs();
    
    for(int i = 0; i < NUM_INPUTS; i++) {
        inputsPins[i].pinState = (inputs >> i) & 1;
        
        if(inputsPins[i].pinState != inputsPins[i].state && inputsPins[i].debouncing == 0) {
            //Debounce
            inputsPins[i].debouncing = 1;
            inputsPins[i].debouncedState = inputsPins[i].pinState;
            inputsPins[i].debounceCounter = buttonDebounceTime;
            
            pinsToDebounce++;
            continue;
        }
        
        //Check for Debounce 
        if(inputsPins[i].debouncing == 1) {
            //Check for the same
            if(inputsPins[i].pinState != inputsPins[i].debouncedState) {
                //The input pin has changed, stop debouncing
                inputsPins[i].debouncing = 0;
                inputsPins[i].debounceCounter = 0;
            } else if(inputsPins[i].debounceCounter == 0) {
                //Set the pin to its new value
                inputsPins[i].debouncing = 0;
                inputsPins[i].debounceCounter = 0;
                inputsPins[i].state = inputsPins[i].debouncedState;
                
                //Notify the ESP32
                PIN_INTERRUPT = 1;
            }
        }
    }
}


void UART_ClearBuffer() {
    for(int i = 0; i < UART_BUFFER_SIZE; i++) {
        rxBuffer[i] = 0;
    }

    rxBufferSize = 0;
}

void UART_Write(char data) {
    while(!TX1STAbits.TRMT); // Wait until the transmit buffer is empty
    TX1REG = data;           // Transmit data
}

void UART_Write_String(const char *str) {
    while(*str != '\0') {
        UART_Write(*str);  // Send each character
        str++;             // Move to the next character
    }
}

void send_uint16_binary(uint16_t data) {
    char binary_string[17]; // 16 bits + null terminator
    binary_string[16] = '\0'; // Null terminator

    for (int i = 15; i >= 0; i--) {
        binary_string[i] = (data & 1) ? '1' : '0';
        data >>= 1;
    }

    // Send each character in the binary string
    for (int i = 0; i < 16; i++) {
        UART_Write(binary_string[i]);
    }
}

uint16_t readInputs(void) {
    
    uint16_t buffer = 0xFFFF;
    
    buffer  = (uint16_t) PORTCbits.RC1 << 7;
    buffer |= (uint16_t) PORTCbits.RC2 << 6;
    buffer |= (uint16_t) PORTCbits.RC3 << 5;
    buffer |= (uint16_t) PORTDbits.RD0 << 4;
    buffer |= (uint16_t) PORTDbits.RD1 << 3;
    buffer |= (uint16_t) PORTDbits.RD2 << 2;
    buffer |= (uint16_t) PORTDbits.RD3 << 1;
    buffer |= (uint16_t) PORTCbits.RC4;
    
    //All inputs are pulled up
    //Invert all bits
    buffer = ~buffer;
    
    /*
        // Combine and shift the necessary bits from PORTC and PORTD directly
        buffer = (PORTC & 0x0F) << 8;  // Mask and shift lower 4 bits of PORTC
        buffer |= (PORTD & 0x7F) << 1; // Mask and shift lower 7 bits of PORTD

        // Extract bit RC4 and place it in the correct position
        buffer |= PORTCbits.RC4 << 3;  // Add RC4 to buffer
    */
    
    return buffer;
    
}

uint16_t readOutputs(void) {
    
    uint16_t buffer = 0;

    buffer |= (uint16_t) PORTBbits.RB3 << 8;
    buffer |= (uint16_t) PORTBbits.RB4 << 7;
    buffer |= (uint16_t) PORTBbits.RB5 << 6;
    buffer |= (uint16_t) PORTAbits.RA7 << 5;
    buffer |= (uint16_t) PORTAbits.RA6 << 4;
    buffer |= (uint16_t) PORTCbits.RC0 << 3;
    buffer |= (uint16_t) PORTDbits.RD4 << 2;
    buffer |= (uint16_t) PORTDbits.RD5 << 1;
    buffer |= (uint16_t) PORTDbits.RD6;
    
    return buffer;
}

void writeOutput(uint8_t id, uint8_t state) {
    switch (id) {
        case 0:
            LATDbits.LATD6 = state;
            break;
        case 1:
            LATDbits.LATD5 = state;
            break;
        case 2:
            LATDbits.LATD4 = state;
            break;
        case 3:
            LATCbits.LATC0 = state;
            break;
        case 4:
            LATAbits.LATA6 = state;
            break;
        case 5:
            LATAbits.LATA7 = state;
            break;
        case 6:
            LATBbits.LATB5 = state;
            break;
        case 7:
            LATBbits.LATB4 = state;
            break;
        case 8:
            LATBbits.LATB3 = state;
            break;
        default:
            break;
    }
}

void parseRxCommand() {
    if (rxBuffer[0] == 'R') {
        //Read all inputs
        //Send 2 bytes for inputs state
        
        uint16_t inputsLastCheckedState = 0x00;

        for(int i = 0; i < NUM_INPUTS; i++) {
            if(inputsPins[i].state == 1) {
                inputsLastCheckedState |= (1 << i);
            }
        }

        PIN_INTERRUPT = 0;
        
        // Split the 12 bits
        uint8_t highByte = (inputsLastCheckedState >> 6) << 2;  // Extract the upper 6 bits, add 0b00 at LSBs
        uint8_t lowByte = inputsLastCheckedState << 2;  		// Extract the lower 6 bits, add 0b00 at LSBs

        UART_Write(OK_BYTE);
        UART_Write(START_BYTE);
        UART_Write(highByte);
        UART_Write(lowByte);
        UART_Write(END_BYTE);
    } else if (rxBuffer[0] == 'O') {
        //Send all outputs values
        UART_Write(OK_BYTE);
        for(int i = 0; i < NUM_OUTPUTS; i++ ) {
            uint16_t value = readOutputs();
            uint8_t highByte = (value >> 8);
            uint8_t lowByte = (value & 0xFF);
        
            UART_Write(highByte);
            UART_Write(lowByte);
        }
    } else if (rxBuffer[0] == 'W') {
        //Write output
        //Byte 1 is the port number 0 - 4 
        //Byte 2 is the port state (0 or 1)
        //Send OK

        if(rxBuffer[1] < NUM_OUTPUTS) {
            //Output state
            if(rxBuffer[2] > 0) {
                writeOutput(rxBuffer[1], 1);
            } else {
                writeOutput(rxBuffer[1], 0);
            }

            UART_Write(OK_BYTE);
        } else {
            UART_Write(ERROR_BYTE);
        }
    } else if (rxBuffer[0] == '3') {
        //Byte 1,2 is Debounce time (0 - 1000ms)
        
        //value is shifted 1 bit so it doesn't collide with START BYTE and END BYTE
        uint16_t value = (((uint16_t)rxBuffer[1] << 8) | rxBuffer[2]) >> 1;
        
        buttonDebounceTime = value;
        
        //Send OK
        UART_Write(OK_BYTE);
    } else if (rxBuffer[0] == 'V') {
        //Version
        UART_Write(OK_BYTE);
        UART_Write(APP_VERSION);
    } else if (rxBuffer[0] == 'X') {
        //Debug info
        char buffer[250];  // Enough space for a 3-digit number (0-255) and null terminator
        
        sprintf(buffer, "Debounce: %u\n", buttonDebounceTime);
        UART_Write_String(buffer);
                
        UART_Write_String("Inputs: ");
        
        uint16_t allInputs = readInputs();
        
        for (int i = 15; i >= 0; i--) {
            if(allInputs & (1 << i)) {
                UART_Write_String("1");
            } else {   
                UART_Write_String("0");
            }
        }
        
        UART_Write_String("\n");
        UART_Write(OK_BYTE);
        
    } else if (rxBuffer[0] == 'Q') {
        UART_Write(OK_BYTE);
        __delay_ms(10);
        __asm("reset");
    } else {
        //Unknown command, return error
        UART_Write(ERROR_COMMAND);
    }

    rxCommandReady = 0;
    UART_ClearBuffer();
}


void main(void) {

    Config();

    while(1) {

        Check_IO();
        
        if(rxCommandReady == 1) {
            parseRxCommand();
        }
        
          
        __delay_us(10);
    }

    return;
}

void timer1Reset() {
    TMR1_ON = 0;
    PIR1bits.TMR1IF = 0;
    TMR1H = 0xB1;     // preset for timer1 MSB register
    TMR1L = 0xE0;     // preset for timer1 LSB register      
    TMR1_ON = 1;
}



// ISR
void __interrupt() ISR(void) {
    // UART receive interrupt
    if (PIR1bits.RC1IF) {
        uint8_t rxByte = RCREG;

        if(rxByte == START_BYTE) {
            UART_ClearBuffer();
        } else if(rxByte == END_BYTE) {
            rxCommandReady = 1;
        } else {
            rxBuffer[rxBufferSize++] = rxByte;
        }

        PIR1bits.RC1IF = 0;   
    }
    
    //Timer1 overflow
    if (PIR1bits.TMR1IF) {
        //First clear the flag, because the commands bellow add up to the timer delay
        timer1Reset();
        
        if(pinsToDebounce > 0) {
            pinsToDebounce = 0;
            for(int i = 0;i < NUM_INPUTS;i++) {
                if(inputsPins[i].debouncing == 1 && inputsPins[i].debounceCounter > 0) {
                    inputsPins[i].debounceCounter--;
                    if(inputsPins[i].debounceCounter > 0) {
                        pinsToDebounce++;
                    }
                }
            }
        }
    }
    
}