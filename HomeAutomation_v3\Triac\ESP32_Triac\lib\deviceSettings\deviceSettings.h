#ifndef DEVICE_SETTINGS_H
#define DEVICE_SETTINGS_H

#include <Arduino.h>
#include <Preferences.h>
#include <IPAddress.h>
#include "global_config.h"


class DeviceSettings {

public:

  // Enum declaration for port types
  enum PortEnum { SWITCH, INVERTED_SWITCH, TOGGLE, HA_BUTTON, TOGGLE_LAST_DIM_STATE, DIM_UP, DIM_DOWN, DIM_ONE_BUTTON };

  // Singleton instance
  static DeviceSettings& getInstance();

  // Begin method to read settings from Preferences
  void begin();

  // Method to get stored values
  byte getAppVersion() const;
  bool getCanbusEnabled() const; 
  bool getMQTTEnabled() const; 
  bool getEventLogEnabled() const;
  bool getDHCPEnabled() const; 
  String getDeviceName() const;
  byte* getDeviceMACAddress() const; // Return pointer to the MAC address array
  IPAddress getMQTTIPAddress() const;
  String getMQTTUsername() const;
  String getMQTTPassword() const;
  byte* getMQTTDeviceID() const;
  PortEnum getPortType(int index) const;
  int getPortMap(int index) const;
  String getPortName(int index) const;

  int getOutputDebounceTime() const;

  IPAddress getStaticIP() const;
  IPAddress getDNSIP() const;
  IPAddress getGatewayIP() const;
  IPAddress getSubnetIP() const;

  // Method to change a value in Preferences
  void setAppVersion(byte version);
  void setCanbusEnabled(bool value);
  void setMQTTEnabled(bool value);
  void setEventLogEnabled(bool value);
  void setDHCPEnabled(bool value);
  void setDeviceName(String name);
  void setDeviceMACAddress(byte* mac);
  void setMQTTIPAddress(IPAddress ip);
  void setMQTTUsername(String username);
  void setMQTTPassword(String password);
  void setMQTTDeviceID(byte* deviceID);
  void setPortType(int index, PortEnum port);
  void setPortMap(int index, int outputPort);
  void setPortName(int index, String name);
  
  void setOutputDebounceTime(uint16_t value);

  void setStaticIP(IPAddress ip);
  void setDNSIP(IPAddress ip);
  void setGatewayIP(IPAddress ip);
  void setSubnetIP(IPAddress ip);

  void setPowerMonitoringEnabled(bool enabled);
  bool isEnergyMonitoringEnabled() const;
  void setOutputPowerConsumption(int index, float watts);
  float getOutputEnergyConsumption(int index) const;

  void resetPreferences();
  
  // Backup and Restore functions
  String backupPreferences();
  void restorePreferences(const String& backupString);

private:
  // Private constructor to enforce singleton pattern
  DeviceSettings();

  // Added method for setting default values
  void setDefaultValues();
  void loadAllValues();

  // Private member variables
  Preferences preferences;
  byte app_version;
  bool mqtt_enabled;
  bool canbus_enabled;
  bool eventlog_enabled;
  bool dhcp_enabled;
  String device_name;
  byte device_mac[6];
  IPAddress mqtt_ip;
  String mqtt_username;
  String mqtt_password;
  byte mqtt_device_id[6];
  IPAddress static_ip;
  IPAddress dns_ip;
  IPAddress gateway_ip;
  IPAddress subnet_ip;
  
  PortEnum portTypes[NUM_INPUTS];
  int portMaps[NUM_INPUTS];
  String portNames[NUM_INPUTS];

  int outputDebounceTime;

  float outputEnergyConsumption[NUM_OUTPUTS];
  bool powerMonitoringEnabled;

};

#endif // DEVICE_SETTINGS_H
