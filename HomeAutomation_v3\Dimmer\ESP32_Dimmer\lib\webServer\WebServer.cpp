#include "WebServer.h"

// Define static variables
IPAddress WebServer::localIP;
float WebServer::temperature = 0.0;
char WebServer::localURL[25] = "http://";

WebServer::WebServer(uint8_t serverPort) {
    _port = serverPort;
}

void WebServer::begin() {
  
    _initialezed = 0;
    _deviceMacAddress = DeviceSettings::getInstance().getDeviceMACAddress();
    Ethernet.init(PIN_SPI_SS_ETHERNET_LIB);

    Serial.printf("Eth init begin\nMac: %02X:%02X:%02X:%02X:%02X:%02X\n", _deviceMacAddress[0], _deviceMacAddress[1], _deviceMacAddress[2], _deviceMacAddress[3], _deviceMacAddress[4], _deviceMacAddress[5]);

    if(DeviceSettings::getInstance().getDHCPEnabled() == true) {
        if (Ethernet.begin(_deviceMacAddress, 1000, 1000))
        { // Dynamic IP setup
            Serial.println("DHCP OK!");
            _initialezed = 1;
        }
        else
        {
            #ifdef DEBUG_TO_UART 
                Serial.println("Failed to configure Ethernet using DHCP");
                // Check for Ethernet hardware present
                if (Ethernet.hardwareStatus() == EthernetNoHardware)
                {
                    Serial.println("Ethernet shield was not found.  Sorry, can't run without hardware. :(");
                }
                if (Ethernet.linkStatus() == LinkOFF)
                {
                    Serial.println("Ethernet cable is not connected.");
                }
            #endif
        }
    } else {
        _initialezed = 1;
        Ethernet.begin(_deviceMacAddress, DeviceSettings::getInstance().getStaticIP(), DeviceSettings::getInstance().getDNSIP(), DeviceSettings::getInstance().getGatewayIP(), DeviceSettings::getInstance().getSubnetIP());
        // Store the local IP address in the static variable
        localIP = Ethernet.localIP();
        sprintf(localURL, "http://%s", localIP.toString().c_str());
    }

#ifdef DEBUG_TO_UART 
    Serial.print("Local IP : ");
    Serial.println(Ethernet.localIP());
    Serial.print("Subnet Mask : ");
    Serial.println(Ethernet.subnetMask());
    Serial.print("Gateway IP : ");
    Serial.println(Ethernet.gatewayIP());
    Serial.print("DNS Server : ");
    Serial.println(Ethernet.dnsServerIP());
    Serial.println("Ethernet Successfully Initialized");
    Serial.println("WebServer Done");
#endif

    if(_initialezed) {
        StatusLed::setPattern(StatusLed::IDLE);
    }

    server.begin(_port);
}

// void WebServer::debug() {
//     Serial.print("NEW  2223 BUILD \n MAC: ");
//     for (int n = 0; n <= 5; n++)
//     {
//         Serial.print(deviceMacAddress[n],HEX);
//         Serial.print(" ");
//     }
//     Serial.println("");
// }

void WebServer::handleFirmwareUpload(EthernetClient client)
{
    while (client.connected())
    {
        if (client.available())
        {
            int command = client.readStringUntil('\n').toInt();
            int content_size = client.readStringUntil('\n').toInt();
            String file_hash = client.readStringUntil('\n');

            client.println("OK");

            if (Update.begin(content_size, U_FLASH))
            {
                Update.setMD5(file_hash.c_str());

                int remaining_bytes = content_size;
                while (remaining_bytes > 0)
                {
                    uint8_t data[1460];
                    int bytes_to_read = min(remaining_bytes, 1460);
                    int bytes_read = client.readBytes(data, bytes_to_read);

                    if (Update.write(data, bytes_read) != bytes_read)
                    {
                        client.println("Firmware update failed.");
                        client.println(Update.getError());
                        Update.abort();
                        return;
                    }
                    remaining_bytes -= bytes_read;
                }

                client.println("OK");
            }
            else
            {
                client.println("Update.begin failed");
            }
        }

        // Finish the update process
        if (Update.end())
        {
            Serial.println("Firmware update successful. Rebooting...");
            client.println("Firmware update successful. Rebooting...");
            client.stop();
            delay(100);
            ESP.restart();
        }
        else
        {
            client.println(Update.getError());
            client.println("Firmware update failed.");
            client.stop();
        }
    }
}

void WebServer::handleFileUpload(EthernetClient client) {

    StatusLed::setPattern(StatusLed::UPDATE);

    //Search for the boundry header
    String boundary;
    int boundarySize = 0;
    while (client.connected())
    {
        //Visual indication that the device is still working
        StatusLed::toggle();

        if (client.available())
        {
            //Read through the headers
            String line = client.readStringUntil('\n');

            if (line.startsWith("Content-Type: multipart/form-data; boundary=")) {
                boundary = line.substring(line.indexOf('=') + 1);
            }

            if(line.startsWith("Content-Length:")) {
                boundarySize = line.substring(line.indexOf(':') + 2).toInt();
            }

            if(line == "\r") {
                //End of headers
                break;
            }
        }
    }

    if (boundary.length() == 0 || boundarySize == 0) {
        client.println("HTTP/1.1 400 Bad Request");
        client.println("Content-Type: text/plain");
        client.println();
        client.println("Missing boundary");
        return;
    }

    bool boundryStartFound = false;
    //Search for boundry start
    while (client.connected())
    {
        //Visual indication that the device is still working
        StatusLed::toggle();

        if (client.available())
        {
            //Read through the headers
            String line = client.readStringUntil('\n');
            Serial.println(line);

            if (line.startsWith("Content-Disposition")) {

                boundryStartFound = true;

                boundarySize -= (boundary.length() + 2) * 2; //Remove the start and end boundary string from the Content-lenght
                boundarySize -= line.length(); //Remove - Content-Disposition: form-data; name="file"; filename="firmware.hex"

                line = client.readStringUntil('\n'); 
                boundarySize -= line.length() + 2; //Remove - Content-Type: application/octet-stream

                line = client.readStringUntil('\n');
                boundarySize -= line.length() + 2; //Remove - Empty line indicating the start of the file content

                boundarySize -= 5; //End ot boundry string "--"
                break;
            }
        }
    }

    Serial.print("Final Boundary Size: ");
    Serial.println(boundarySize);

    if (boundryStartFound == false) {
        client.println("HTTP/1.1 400 Bad Request");
        client.println("Content-Type: text/plain");
        client.println();
        client.println("Missing boundary start");
        return;
    }

    //Visual indication that the device is still working
    StatusLed::toggle();

    if (Update.begin(boundarySize, U_FLASH))
    {
        //Update.setMD5("06040a3de46bd56d7dd061ca09e2049f");

        int remaining_bytes = boundarySize;
        while (remaining_bytes > 0)
        {
            //Visual indication that the device is still working
            StatusLed::toggle();

            uint8_t data[1024];
            int bytes_to_read = min(remaining_bytes, 1024);
            int bytes_read = client.readBytes(data, bytes_to_read);

            if (Update.write(data, bytes_read) != bytes_read)
            {
                client.println("Firmware update failed.");
                client.println(Update.getError());
                Update.abort();
                return;
            }
            remaining_bytes -= bytes_read;
        }

        client.println("OK");
    }
    else
    {
        client.println("Update.begin failed");
    }

    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/html");
    client.println();

    // Finish the update process
    if (Update.end())
    {
        Serial.println("Firmware update successful. Rebooting...");

        client.println("File uploaded successfully. Rebooting... <br><a href='/'>Reload</a>");
        client.stop();

        delay(1000);

        ESP.restart();
    }
    else
    {
        client.println(Update.getError());
        client.println("Firmware update failed.");
        client.stop();
    }
}


void WebServer::handleIndexFile(EthernetClient client) {
    //Headers
    client.println("HTTP/1.1 200 OK");
    client.println("Content-Encoding: gzip");
    client.println("Content-Type: text/html");
    client.print("Content-Length: ");
    client.println(index_html_gz_len);

    client.println();

    // Send data in chunks , because the W5500 buff size is 2048
    size_t chunkSize = 2000;
    for (size_t i = 0; i < index_html_gz_len; i += chunkSize) {
        size_t remaining = min(chunkSize, index_html_gz_len - i);
        client.write(index_html_gz + i, remaining);
    }
}

void WebServer::handleAjaxInit(EthernetClient client) {
    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();

    uint16_t inputPorts = IOModule::getInstance().inputPorts;

    client.print("{\"inputs\":[");
    for (int i = 0; i < 12; i++) {
        // Extract the bit at position i for both variables
        uint8_t bit1 = (inputPorts >> i) & 0x01;
        client.print(bit1, DEC);
        if(i < 11) {
            client.print(",");
        }
    }
    client.print("],\"outputs\":[");
    for (int i = 0; i < 5; i++) {
        // Extract the bit at position i for both variables
        client.print(IOModule::getInstance().lastDimValue[i], DEC);
        if(i < 4) {
            client.print(",");
        }
    }

    client.print("],\"uptime\":\"Uptime: ");
    
    int seconds = 0;
    int minutes = 0;
    int hours = 0;
    int days = 0;
    int32_t esp32_seconds_since_start = esp_timer_get_time() / 1000000;

    if(esp32_seconds_since_start > 86400) {
        days = esp32_seconds_since_start / 86400;
        esp32_seconds_since_start = esp32_seconds_since_start % 86400;
    }

    if(esp32_seconds_since_start > 3600) {
        hours = esp32_seconds_since_start / 3600;
        esp32_seconds_since_start = esp32_seconds_since_start % 3600;
    }

    if(esp32_seconds_since_start > 60) {
        minutes = esp32_seconds_since_start / 60;
        esp32_seconds_since_start = esp32_seconds_since_start % 60;
    }

    seconds = esp32_seconds_since_start;
    
    client.print(days);
    client.print("d ");
    client.print(hours);
    client.print("h ");
    client.print(minutes);
    client.print("m ");
    client.print(seconds);
    client.print("s");
    client.print("\"}");
}

void WebServer::handleAjaxSettings(EthernetClient client) {
    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();

    client.print("{\"appVersion\":");
    client.print(DeviceSettings::getInstance().getAppVersion());

    client.print(",\"picVersion\":");
    client.print(IOModule::getInstance().picVersion);

    client.print(",\"canBusEnable\":");
    client.print(DeviceSettings::getInstance().getCanbusEnabled());

    client.print(",\"mqttEnable\":");
    client.print(DeviceSettings::getInstance().getMQTTEnabled());
      
    client.print(",\"eventLogEnabled\":");
    client.print(DeviceSettings::getInstance().getEventLogEnabled());

    client.print(",\"dhcpEnabled\":");
    client.print(DeviceSettings::getInstance().getDHCPEnabled());

    client.print(",\"deviceName\":\"");
    client.print(DeviceSettings::getInstance().getDeviceName());

    client.print("\",\"macAddress\":\"");

    byte* macAddress = DeviceSettings::getInstance().getDeviceMACAddress();
    for(int i = 0; i < 6; i++) {
        client.print(macAddress[i], HEX);
        if(i< 5) {
            client.print(":");
        }
    }

    client.print("\",\"mqttIPAddress\":\"");
    client.print(DeviceSettings::getInstance().getMQTTIPAddress().toString());
  
    client.print("\",\"mqttUsername\":\"");
    client.print(DeviceSettings::getInstance().getMQTTUsername());
    client.print("\",\"mqttPassword\":\"");
    client.print(DeviceSettings::getInstance().getMQTTPassword());
    client.print("\",\"mqttDeviceId\":\"");


    byte* mqttDeviceID = DeviceSettings::getInstance().getMQTTDeviceID();
    for(int i = 0; i < 6; i++) {
        client.print(mqttDeviceID[i], HEX);
        if(i< 5) {
            client.print(":");
        }
    }

    client.print("\",\"staticIP\":\"");
    client.print(DeviceSettings::getInstance().getStaticIP().toString());
    client.print("\",\"dnsIP\":\"");
    client.print(DeviceSettings::getInstance().getDNSIP().toString());
    client.print("\",\"gatewayIP\":\"");
    client.print(DeviceSettings::getInstance().getGatewayIP().toString());
    client.print("\",\"subnetIP\":\"");
    client.print(DeviceSettings::getInstance().getSubnetIP().toString());


    client.print("\",\"portTypes\": [");
    for(int i = 0; i < NUM_INPUTS; i++) {
        client.print(DeviceSettings::getInstance().getPortType(i));
        if(i < (NUM_INPUTS - 1)) {
            client.print(",");
        }
    }

    client.print("],\"portNames\": [");
    for(int i = 0; i < NUM_INPUTS; i++) {
        client.print("\"");
        client.print(DeviceSettings::getInstance().getPortName(i));
        client.print("\"");
        if(i < (NUM_INPUTS - 1)) {
            client.print(",");
        }
    }


    client.print("],\"portMaps\": [");
    for(int i = 0; i < NUM_INPUTS; i++) {
        client.print(DeviceSettings::getInstance().getPortMap(i));
        if(i < (NUM_INPUTS - 1)) {
            client.print(",");
        }
    }

    client.print("],\"outputType\": [");
    for(int i = 0; i < NUM_OUTPUTS; i++) {
        client.print(DeviceSettings::getInstance().getOutputType(i));
        if(i < (NUM_OUTPUTS - 1)) {
            client.print(",");
        }
    }

    client.print("],\"outputMin\": [");
    for(int i = 0; i < NUM_OUTPUTS; i++) {
        client.print(DeviceSettings::getInstance().getOutputMin(i));
        if(i < (NUM_OUTPUTS - 1)) {
            client.print(",");
        }
    }

    client.print("],\"outputMax\": [");
    for(int i = 0; i < NUM_OUTPUTS; i++) {
        client.print(DeviceSettings::getInstance().getOutputMax(i));
        if(i < (NUM_OUTPUTS - 1)) {
            client.print(",");
        }
    }

    client.print("],\"outputDebounce\":");
    client.print(DeviceSettings::getInstance().getOutputDebounceTime());
    client.print(",\"outputFadeInSpeed\":");
    client.print(DeviceSettings::getInstance().getOutputFadeInSpeed());
    client.print(",\"outputFadeOutSpeed\":");
    client.print(DeviceSettings::getInstance().getOutputFadeOutSpeed());
    client.print("}");

}

void WebServer::handleAjaxDebug(EthernetClient client) {
    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/plain");
    client.println();

    //uint16_t inputPorts = IOModule::getInstance().inputPorts;
    //uint16_t outputPorts = IOModule::getInstance().outputPorts;
    
    EventManager::getInstance().printEvents(client);
}

void WebServer::handleAjaxSaveMQTT(EthernetClient client) {

    int bodySize = 0;
    while (client.connected())
    {
        if (client.available())
        {
            //Read through the headers
            String headers = client.readStringUntil('\n');
            
            if(headers.startsWith("Content-Length:")) {
                bodySize = headers.substring(headers.indexOf(':') + 2).toInt();
            } else if(headers == "\r") {
                //End of headers
                break;
            }
        }
    }

    //mqttEnable=1&mqttIPAddress=0.0.0.0&mqttUsername=&mqttPassword=&mqttDeviceId=

    char formByteArray[bodySize];
    client.readBytes(formByteArray, bodySize);
    formByteArray[bodySize] = '\0'; //Clear the last byte of the buffer
    String formData = urlDecode(String(formByteArray));
    Serial.println(formData);
    String variables[10]; // Adjust size as needed

    // Split the string
    int numVariables = splitString(formData, '&', variables, 10);

    for (int i = 0; i < numVariables; i++) {

        int index = variables[i].indexOf("=");
        String name = variables[i].substring(0, index);
        String value = variables[i].substring(index + 1);

        if(name == "mqttEnable") {
            DeviceSettings::getInstance().setMQTTEnabled(value == "1");
        } else if(name == "mqttIPAddress") {
            IPAddress ip;
            ip.fromString(value);
            DeviceSettings::getInstance().setMQTTIPAddress(ip);
        } else if(name == "mqttUsername") {
            DeviceSettings::getInstance().setMQTTUsername(value);
        } else if(name == "mqttPassword") {
            DeviceSettings::getInstance().setMQTTPassword(value);
        } else if(name == "mqttDeviceId") {
            String macBytes[6];
            int numMacBytes = splitString(value, ':', macBytes, 6);
            if(numMacBytes == 6)  {
                byte macAddress[6];
                for(int i=0;i<6;i++) {
                    macAddress[i] = hexCharToint(macBytes[i].charAt(0))*16 + hexCharToint(macBytes[i].charAt(1));
                }
                DeviceSettings::getInstance().setMQTTDeviceID(macAddress);
            }
        }
    }

    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();
    client.println("OK");
}

void WebServer::handleAjaxSaveNetwork(EthernetClient client) {

    int bodySize = 0;
    while (client.connected())
    {
        if (client.available())
        {
            //Read through the headers
            String headers = client.readStringUntil('\n');
            
            if(headers.startsWith("Content-Length:")) {
                bodySize = headers.substring(headers.indexOf(':') + 2).toInt();
            } else if(headers == "\r") {
                //End of headers
                break;
            }
        }
    }

	//dhcpEnabled=1&deviceName=DFevice&deviceMac=4B:6E:FF:AA:7E:2D&staticIP=0.0.0.0&subnetIP=0.0.0.0&gatewayIP=0.0.0.0&dnsIP=0.0.0.0

    char formByteArray[bodySize];
    client.readBytes(formByteArray, bodySize);
    formByteArray[bodySize] = '\0'; //Clear the last byte of the buffer
    String formData = urlDecode(String(formByteArray));
    Serial.println(formData);

    String variables[10]; // Adjust size as needed

    // Split the string
    int numVariables = splitString(formData, '&', variables, 10);

    for (int i = 0; i < numVariables; i++) {

        int index = variables[i].indexOf("=");
        String name = variables[i].substring(0, index);
        String value = variables[i].substring(index + 1);

        if(name == "dhcpEnabled") {
            DeviceSettings::getInstance().setDHCPEnabled(value == "1");

        } else if(name == "deviceName") {
            DeviceSettings::getInstance().setDeviceName(value);
        } else if(name == "deviceMac") {
            String macBytes[6];
            int numMacBytes = splitString(value, ':', macBytes, 6);
            if(numMacBytes == 6)  {
                byte macAddress[6];
                for(int i=0;i<6;i++) {
                    macAddress[i] = hexCharToint(macBytes[i].charAt(0))*16 + hexCharToint(macBytes[i].charAt(1));
                }
                DeviceSettings::getInstance().setDeviceMACAddress(macAddress);
            }
        } else if(name == "staticIP") {
            IPAddress ip;
            ip.fromString(value);
            DeviceSettings::getInstance().setStaticIP(ip);
        } else if(name == "subnetIP") {
            IPAddress ip;
            ip.fromString(value);
            DeviceSettings::getInstance().setSubnetIP(ip);
        }  else if(name == "gatewayIP") {
            IPAddress ip;
            ip.fromString(value);
            DeviceSettings::getInstance().setGatewayIP(ip);
        }  else if(name == "dnsIP") {
            IPAddress ip;
            ip.fromString(value);
            DeviceSettings::getInstance().setDNSIP(ip);
        }
        
    }

    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();
    client.println("OK");
}

void WebServer::handleAjaxSetOutput(EthernetClient client) {
    int bodySize = 0;
    while (client.connected())
    {
        if (client.available())
        {
            //Read through the headers
            String headers = client.readStringUntil('\n');
            
            if(headers.startsWith("Content-Length:")) {
                bodySize = headers.substring(headers.indexOf(':') + 2).toInt();
            } else if(headers == "\r") {
                //End of headers
                break;
            }
        }
    }

    //outputValue=500&outputId=4
    char formByteArray[bodySize];
    client.readBytes(formByteArray, bodySize);
    formByteArray[bodySize] = '\0'; //Clear the last byte of the buffer
    String formData = String(formByteArray);
    Serial.println(formData);
    String variables[2]; // Adjust size as needed

    // Split the string
    int numVariables = splitString(formData, '&', variables, 2);

    uint8_t outputId = 255;
    uint16_t outputValue = 65535;

    for (int i = 0; i < numVariables; i++) {

        int index = variables[i].indexOf("=");
        String name = variables[i].substring(0, index);
        String value = variables[i].substring(index + 1);

        if(name == "outputId") {
            outputId = atoi(value.c_str());
        } else if(name == "outputValue") {
            outputValue = atoi(value.c_str());
        }
    }

    EventManager::getInstance().logEvent(EventManager::WEB, outputId, (outputValue / DIMMER_MAX_STEP));

    if(outputId < 255 and outputValue < 65535) {
        IOModule::getInstance().setDimmer(outputId, outputValue);
    }

    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();
    client.println("OK");
}

void WebServer::handleAjaxSaveCanBus(EthernetClient client) {
    int bodySize = 0;
    while (client.connected())
    {
        if (client.available())
        {
            //Read through the headers
            String headers = client.readStringUntil('\n');
            
            if(headers.startsWith("Content-Length:")) {
                bodySize = headers.substring(headers.indexOf(':') + 2).toInt();
            } else if(headers == "\r") {
                //End of headers
                break;
            }
        }
    }

    //canbusEnable=1
    char formByteArray[bodySize];
    client.readBytes(formByteArray, bodySize);
    formByteArray[bodySize] = '\0'; //Clear the last byte of the buffer
    String formData = String(formByteArray);
    Serial.println(formData);
    String variables[2]; // Adjust size as needed

    // Split the string
    int numVariables = splitString(formData, '&', variables, 2);

    for (int i = 0; i < numVariables; i++) {

        int index = variables[i].indexOf("=");
        String name = variables[i].substring(0, index);
        String value = variables[i].substring(index + 1);

        if(name == "canbusEnable") {
            DeviceSettings::getInstance().setCanbusEnabled(value.toInt() == 1);
        }
    }

    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();
    client.println("OK");
    
}

void WebServer::handleAjaxSaveEventlog(EthernetClient client) {
    int bodySize = 0;
    while (client.connected())
    {
        if (client.available())
        {
            //Read through the headers
            String headers = client.readStringUntil('\n');
            
            if(headers.startsWith("Content-Length:")) {
                bodySize = headers.substring(headers.indexOf(':') + 2).toInt();
            } else if(headers == "\r") {
                //End of headers
                break;
            }
        }
    }

    //eventlogEnable=1
    char formByteArray[bodySize];
    client.readBytes(formByteArray, bodySize);
    formByteArray[bodySize] = '\0'; //Clear the last byte of the buffer
    String formData = String(formByteArray);
    Serial.println(formData);
    String variables[2]; // Adjust size as needed

    // Split the string
    int numVariables = splitString(formData, '&', variables, 2);

    for (int i = 0; i < numVariables; i++) {

        int index = variables[i].indexOf("=");
        String name = variables[i].substring(0, index);
        String value = variables[i].substring(index + 1);

        if(name == "eventlogEnable") {
            DeviceSettings::getInstance().setEventLogEnabled(value.toInt() == 1);
        }
    }

    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();
    client.println("OK");
}

void WebServer::handleAjaxSavePorts(EthernetClient client) {
    
    int bodySize = 0;
    while (client.connected())
    {
        if (client.available())
        {
            //Read through the headers
            String headers = client.readStringUntil('\n');
            
            if(headers.startsWith("Content-Length:")) {
                bodySize = headers.substring(headers.indexOf(':') + 2).toInt();
            } else if(headers == "\r") {
                //End of headers
                break;
            }
        }
    }

	//port_name_0=Input1&port_type_0=0&port_map_0=9&port_name_1=Input2&port_type_1=0&port_map_1=9&port_name_2=Input3&port_type_2=0&port_map_2=9&port_name_3=Input4&port_type_3=0&port_map_3=9&port_name_4=Input5&port_type_4=0&port_map_4=9&port_name_5=Input6&port_type_5=0&port_map_5=9&
    //port_name_6=Input7&port_type_6=0&port_map_6=9&port_name_7=Input8&port_type_7=0&port_map_7=9&port_name_8=Input9&port_type_8=0&port_map_8=9&port_name_9=Input10&port_type_9=0&port_map_9=9&port_name_10=Input11&port_type_10=0&port_map_10=9&port_name_11=Input12&port_type_11=0&port_map_11=9
    char formByteArray[bodySize];
    client.readBytes(formByteArray, bodySize);
    formByteArray[bodySize] = '\0';
    String formData = urlDecode(String(formByteArray));
    Serial.println(formData);

    String variables[NUM_INPUTS * 3]; // Adjust size as needed

    // Split the string
    int numVariables = splitString(formData, '&', variables, NUM_INPUTS * 3);


    for (int i = 0; i < numVariables; i++) {

        int index = variables[i].indexOf("=");
        String name = variables[i].substring(0, index);
        String value = variables[i].substring(index + 1);

        if(name.startsWith("port_name_")) {
            name.replace("port_name_", "");
            int port_num = name.toInt();
            DeviceSettings::getInstance().setPortName(port_num, value);
        } else if(name.startsWith("port_type_")) {
            name.replace("port_type_", "");
            int port_num = name.toInt();
            DeviceSettings::getInstance().setPortType(port_num, static_cast<DeviceSettings::PortEnum>(value.toInt()));
        } else if(name.startsWith("port_map_")) {
            name.replace("port_map_", "");
            int port_num = name.toInt();
            DeviceSettings::getInstance().setPortMap(port_num, value.toInt());
        }
    }

    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();
    client.println("OK");
}

void WebServer::handleAjaxSaveOutputPorts(EthernetClient client) {
    
    int bodySize = 0;
    while (client.connected())
    {
        if (client.available())
        {
            //Read through the headers
            String headers = client.readStringUntil('\n');
            
            if(headers.startsWith("Content-Length:")) {
                bodySize = headers.substring(headers.indexOf(':') + 2).toInt();
            } else if(headers == "\r") {
                //End of headers
                break;
            }
        }
    }

	//output_min_0=1&output_max_0=6&output_type_0=0&output_min_1=2&output_max_1=7&output_type_1=0&output_min_2=3&output_max_2=8&output_type_2=0&output_min_3=4&output_max_3=9&output_type_3=0&output_min_4=5&output_max_4=10&output_type_4=0&fade_in_speed=11&fade_out_speed=12&debounce=13

    char formByteArray[bodySize];
    client.readBytes(formByteArray, bodySize);
    formByteArray[bodySize] = '\0';
    String formData = urlDecode(String(formByteArray));
    //Serial.println(formData);

    String variables[NUM_OUTPUTS * 5]; // Adjust size as needed

    // Split the string
    int numVariables = splitString(formData, '&', variables, NUM_OUTPUTS * 5);


    for (int i = 0; i < numVariables; i++) {

        int index = variables[i].indexOf("=");
        String name = variables[i].substring(0, index);
        String value = variables[i].substring(index + 1);

        if(name.startsWith("output_min_")) {
            name.replace("output_min_", "");
            int output_num = name.toInt();
            DeviceSettings::getInstance().setOutputMin(output_num, value.toInt());
        } else if(name.startsWith("output_max_")) {
            name.replace("output_max_", "");
            int output_num = name.toInt();
            DeviceSettings::getInstance().setOutputMax(output_num, value.toInt());
        } else if(name.startsWith("output_type_")) {
            name.replace("output_type_", "");
            int output_num = name.toInt();
            DeviceSettings::getInstance().setOutputType(output_num, value.toInt());
            IOModule::getInstance().setChannelType(output_num, value.toInt());
        } else if(name.startsWith("fade_in_speed")) {
            DeviceSettings::getInstance().setOutputFadeInSpeed(value.toInt());
            IOModule::getInstance().setFadeInSpeed(value.toInt());
        } else if(name.startsWith("fade_out_speed")) {
            DeviceSettings::getInstance().setOutputFadeOutSpeed(value.toInt());
            IOModule::getInstance().setFadeOutSpeed(value.toInt());
        } else if(name.startsWith("debounce")) {
            DeviceSettings::getInstance().setOutputDebounceTime(value.toInt());
            IOModule::getInstance().setDebounceTime(value.toInt());
        }
    }

    client.println("HTTP/1.1 200 OK");
    client.println("Content-Type: text/json");
    client.println();
    client.println("OK");
}

void WebServer::loop()
{

    if(_initialezed == 0) {
        begin();
    }

    //Check the dhcp lease
    if(_initialezed && DeviceSettings::getInstance().getDHCPEnabled()) {
        Ethernet.maintain();
    }


    EthernetClient client = server.available();
    if (client)
    {
        if (client.connected())
        {
            String currentLine = "";
            while (client.connected())
            {
                if (client.available())
                {
                    char c = client.read();
                    currentLine += c;
                    if (c == '\n')
                    {
                        if (currentLine.indexOf("GET /ajax-init") != -1)
                        {
                            handleAjaxInit(client);
                        }
                        else if (currentLine.indexOf("GET /ajax-settings") != -1)
                        {
                            handleAjaxSettings(client);
                        }
                        else if (currentLine.indexOf("GET /ajax-debug") != -1)
                        {
                            handleAjaxDebug(client);
                        }
                        else if (currentLine.indexOf("POST /ajax-save-mqtt") != -1)
                        {
                            handleAjaxSaveMQTT(client);
                        }
                        else if (currentLine.indexOf("POST /ajax-save-network") != -1)
                        {
                            handleAjaxSaveNetwork(client);
                        }
                        else if (currentLine.indexOf("POST /ajax-set-output") != -1)
                        {
                            handleAjaxSetOutput(client);
                        }
                        else if (currentLine.indexOf("POST /ajax-save-canbus") != -1)
                        {
                            handleAjaxSaveCanBus(client);
                        }
                        else if (currentLine.indexOf("POST /ajax-save-eventlog") != -1)
                        {
                            handleAjaxSaveEventlog(client);
                        }
                        else if (currentLine.indexOf("POST /ajax-save-ports") != -1)
                        {
                            handleAjaxSavePorts(client);
                        }
                        else if (currentLine.indexOf("POST /ajax-save-output-ports") != -1)
                        {
                            handleAjaxSaveOutputPorts(client);
                        }
                        else if (currentLine.indexOf("GET /reboot") != -1)
                        {
                            
                            client.println("HTTP/1.1 200");
                            client.println("Content-Type: text/html");
                            client.println("");
                            client.println("OK");
                            client.stop();

                            IOModule::getInstance().reboot();

                            ESP.restart();
                        } 
                        else if (currentLine.indexOf("GET /default-settings") != -1)
                        {
                            client.println("HTTP/1.1 200");
                            client.println("Content-Type: text/html");
                            client.println("");
                            client.println("OK");
                            
                            DeviceSettings::getInstance().resetPreferences();
                        } 
                        else if (currentLine.indexOf("POST /upload") != -1)
                        {
                            // Firmware upload request detected
                            handleFirmwareUpload(client);
                        } 
                        else if (currentLine.indexOf("POST /firmware-upload") != -1)
                        {
                            // Firmware upload request detected
                            handleFileUpload(client);
                        }
                        else if (currentLine.indexOf("POST /pic-firmware-upload") != -1)
                        {
                            // Firmware upload request detected
                            handleFileUpload(client);
                        } 
                        else if (currentLine.indexOf("GET / ") != -1)
                        {
                            //Serve index.html
                            handleIndexFile(client);
                        } else {
                            client.println("HTTP/1.1 404 Not Found");
                            client.println("Content-Type: text/html");
                        }
                        
                        break;
                    }
                }
            }
            client.stop();
        }
    }
}



int WebServer::splitString(String data, char delimiter, String* tokens, int maxTokens) {
  int start = 0;
  int end = data.indexOf(delimiter);
  int tokenIndex = 0;

  while (end != -1) {
    if (tokenIndex < maxTokens) {
      tokens[tokenIndex] = data.substring(start, end);
      tokenIndex++;
    }
    start = end + 1;
    end = data.indexOf(delimiter, start);
  }

  // Add the last token
  if (tokenIndex < maxTokens) {
    tokens[tokenIndex] = data.substring(start);
    tokenIndex++;
  }

  return tokenIndex; // Return the number of tokens found
}

String WebServer::urlDecode(String urlChars)
{

  urlChars.replace("%0D%0A", String('\n'));

  urlChars.replace("+",   " ");
  urlChars.replace("%20", " ");
  urlChars.replace("%21", "!");
  urlChars.replace("%22", String(char('\"')));
  urlChars.replace("%23", "#");
  urlChars.replace("%24", "$");
  urlChars.replace("%25", "%");
  urlChars.replace("%26", "&");
  urlChars.replace("%27", String(char(39)));
  urlChars.replace("%28", "(");
  urlChars.replace("%29", ")");
  urlChars.replace("%2A", "*");
  urlChars.replace("%2B", "+");
  urlChars.replace("%2C", ",");
  urlChars.replace("%2D", "-");
  urlChars.replace("%2E", ".");
  urlChars.replace("%2F", "/");
  //urlChars.replace("%30", "0");
  //urlChars.replace("%31", "1");
  //urlChars.replace("%32", "2");
  //urlChars.replace("%33", "3");
  //urlChars.replace("%34", "4");
  //urlChars.replace("%35", "5");
  //urlChars.replace("%36", "6");
  //urlChars.replace("%37", "7");
  //urlChars.replace("%38", "8");
  //urlChars.replace("%39", "9");
  urlChars.replace("%3A", ":");
  urlChars.replace("%3B", ";");
  urlChars.replace("%3C", "<");
  urlChars.replace("%3D", "=");
  urlChars.replace("%3E", ">");
  urlChars.replace("%3F", "?");
  urlChars.replace("%40", "@");
  //urlChars.replace("%41", "A");
  //urlChars.replace("%42", "B");
  //urlChars.replace("%43", "C");
  //urlChars.replace("%44", "D");
  //urlChars.replace("%45", "E");
  //urlChars.replace("%46", "F");
  //urlChars.replace("%47", "G");
  //urlChars.replace("%48", "H");
  //urlChars.replace("%49", "I");
  //urlChars.replace("%4A", "J");
  //urlChars.replace("%4B", "K");
  //urlChars.replace("%4C", "L");
  //urlChars.replace("%4D", "M");
  //urlChars.replace("%4E", "N");
  //urlChars.replace("%4F", "O");
  //urlChars.replace("%50", "P");
  //urlChars.replace("%51", "Q");
  //urlChars.replace("%52", "R");
  //urlChars.replace("%53", "S");
  //urlChars.replace("%54", "T");
  //urlChars.replace("%55", "U");
  //urlChars.replace("%56", "V");
  //urlChars.replace("%57", "W");
  //urlChars.replace("%58", "X");
  //urlChars.replace("%59", "Y");
  //urlChars.replace("%5A", "Z");
  urlChars.replace("%5B", "[");
  urlChars.replace("%5C", String(char(65)));
  urlChars.replace("%5D", "]");
  urlChars.replace("%5E", "^");
  urlChars.replace("%5F", "_");
  urlChars.replace("%60", "`");
  //urlChars.replace("%61", "a");
  //urlChars.replace("%62", "b");
  //urlChars.replace("%63", "c");
  //urlChars.replace("%64", "d");
  //urlChars.replace("%65", "e");
  //urlChars.replace("%66", "f");
  //urlChars.replace("%67", "g");
  //urlChars.replace("%68", "h");
  //urlChars.replace("%69", "i");
  //urlChars.replace("%6A", "j");
  //urlChars.replace("%6B", "k");
  //urlChars.replace("%6C", "l");
  //urlChars.replace("%6D", "m");
  //urlChars.replace("%6E", "n");
  //urlChars.replace("%6F", "o");
  //urlChars.replace("%70", "p");
  //urlChars.replace("%71", "q");
  //urlChars.replace("%72", "r");
  //urlChars.replace("%73", "s");
  //urlChars.replace("%74", "t");
  //urlChars.replace("%75", "u");
  //urlChars.replace("%76", "v");
  //urlChars.replace("%77", "w");
  //urlChars.replace("%78", "x");
  //urlChars.replace("%79", "y");
  //urlChars.replace("%7A", "z");
  //urlChars.replace("%7B", String(char(123)));
  //urlChars.replace("%7C", "|");
  //urlChars.replace("%7D", String(char(125)));
  //urlChars.replace("%7E", "~");
  //urlChars.replace("%7F", "Â");
  //urlChars.replace("%80", "`");
  //urlChars.replace("%81", "Â");
  //urlChars.replace("%82", "â");
  //urlChars.replace("%83", "Æ");
  //urlChars.replace("%84", "â");
  //urlChars.replace("%85", "â¦");
  //urlChars.replace("%86", "â");
  //urlChars.replace("%87", "â¡");
  //urlChars.replace("%88", "Ë");
  //urlChars.replace("%89", "â°");
  //urlChars.replace("%8A", "Å");
  //urlChars.replace("%8B", "â¹");
  //urlChars.replace("%8C", "Å");
  //urlChars.replace("%8D", "Â");
  //urlChars.replace("%8E", "Å½");
  //urlChars.replace("%8F", "Â");
  //urlChars.replace("%90", "Â");
  //urlChars.replace("%91", "â");
  //urlChars.replace("%92", "â");
  //urlChars.replace("%93", "â");
  //urlChars.replace("%94", "â");
  //urlChars.replace("%95", "â¢");
  //urlChars.replace("%96", "â");
  //urlChars.replace("%97", "â");
  //urlChars.replace("%98", "Ë");
  //urlChars.replace("%99", "â¢");
  //urlChars.replace("%9A", "Å¡");
  //urlChars.replace("%9B", "âº");
  //urlChars.replace("%9C", "Å");
  //urlChars.replace("%9D", "Â");
  //urlChars.replace("%9E", "Å¾");
  //urlChars.replace("%9F", "Å¸");
  //urlChars.replace("%A0", "Â");
  //urlChars.replace("%A1", "Â¡");
  //urlChars.replace("%A2", "Â¢");
  //urlChars.replace("%A3", "Â£");
  //urlChars.replace("%A4", "Â¤");
  //urlChars.replace("%A5", "Â¥");
  //urlChars.replace("%A6", "Â¦");
  //urlChars.replace("%A7", "Â§");
  //urlChars.replace("%A8", "Â¨");
  //urlChars.replace("%A9", "Â©");
  //urlChars.replace("%AA", "Âª");
  //urlChars.replace("%AB", "Â«");
  //urlChars.replace("%AC", "Â¬");
  //urlChars.replace("%AE", "Â®");
  //urlChars.replace("%AF", "Â¯");
  //urlChars.replace("%B0", "Â°");
  //urlChars.replace("%B1", "Â±");
  //urlChars.replace("%B2", "Â²");
  //urlChars.replace("%B3", "Â³");
  //urlChars.replace("%B4", "Â´");
  //urlChars.replace("%B5", "Âµ");
  //urlChars.replace("%B6", "Â¶");
  //urlChars.replace("%B7", "Â·");
  //urlChars.replace("%B8", "Â¸");
  //urlChars.replace("%B9", "Â¹");
  //urlChars.replace("%BA", "Âº");
  //urlChars.replace("%BB", "Â»");
  //urlChars.replace("%BC", "Â¼");
  //urlChars.replace("%BD", "Â½");
  //urlChars.replace("%BE", "Â¾");
  //urlChars.replace("%BF", "Â¿");
  //urlChars.replace("%C0", "Ã");
  //urlChars.replace("%C1", "Ã");
  //urlChars.replace("%C2", "Ã");
  //urlChars.replace("%C3", "Ã");
  //urlChars.replace("%C4", "Ã");
  //urlChars.replace("%C5", "Ã");
  //urlChars.replace("%C6", "Ã");
  //urlChars.replace("%C7", "Ã");
  //urlChars.replace("%C8", "Ã");
  //urlChars.replace("%C9", "Ã");
  //urlChars.replace("%CA", "Ã");
  //urlChars.replace("%CB", "Ã");
  //urlChars.replace("%CC", "Ã");
  //urlChars.replace("%CD", "Ã");
  //urlChars.replace("%CE", "Ã");
  //urlChars.replace("%CF", "Ã");
  //urlChars.replace("%D0", "Ã");
  //urlChars.replace("%D1", "Ã");
  //urlChars.replace("%D2", "Ã");
  //urlChars.replace("%D3", "Ã");
  //urlChars.replace("%D4", "Ã");
  //urlChars.replace("%D5", "Ã");
  //urlChars.replace("%D6", "Ã");
  //urlChars.replace("%D7", "Ã");
  //urlChars.replace("%D8", "Ã");
  //urlChars.replace("%D9", "Ã");
  //urlChars.replace("%DA", "Ã");
  //urlChars.replace("%DB", "Ã");
  //urlChars.replace("%DC", "Ã");
  //urlChars.replace("%DD", "Ã");
  //urlChars.replace("%DE", "Ã");
  //urlChars.replace("%DF", "Ã");
  //urlChars.replace("%E0", "Ã");
  //urlChars.replace("%E1", "Ã¡");
  //urlChars.replace("%E2", "Ã¢");
  //urlChars.replace("%E3", "Ã£");
  //urlChars.replace("%E4", "Ã¤");
  //urlChars.replace("%E5", "Ã¥");
  //urlChars.replace("%E6", "Ã¦");
  //urlChars.replace("%E7", "Ã§");
  //urlChars.replace("%E8", "Ã¨");
  //urlChars.replace("%E9", "Ã©");
  //urlChars.replace("%EA", "Ãª");
  //urlChars.replace("%EB", "Ã«");
  //urlChars.replace("%EC", "Ã¬");
  //urlChars.replace("%ED", "Ã­");
  //urlChars.replace("%EE", "Ã®");
  //urlChars.replace("%EF", "Ã¯");
  //urlChars.replace("%F0", "Ã°");
  //urlChars.replace("%F1", "Ã±");
  //urlChars.replace("%F2", "Ã²");
  //urlChars.replace("%F3", "Ã³");
  //urlChars.replace("%F4", "Ã´");
  //urlChars.replace("%F5", "Ãµ");
  //urlChars.replace("%F6", "Ã¶");
  //urlChars.replace("%F7", "Ã·");
  //urlChars.replace("%F8", "Ã¸");
  //urlChars.replace("%F9", "Ã¹");
  //urlChars.replace("%FA", "Ãº");
  //urlChars.replace("%FB", "Ã»");
  //urlChars.replace("%FC", "Ã¼");
  //urlChars.replace("%FD", "Ã½");
  //urlChars.replace("%FE", "Ã¾");
  //urlChars.replace("%FF", "Ã¿");

  return urlChars;
}


int WebServer::hexCharToint(char input)
{
  if(input >= '0' && input <= '9')
    return input - '0';
  if(input >= 'A' && input <= 'F')
    return input - 'A' + 10;
  if(input >= 'a' && input <= 'f')
    return input - 'a' + 10;

  return 0;
}
