#pragma once

#include <ACAN_ESP32.h>
#include "canProtocol.h"
#include "deviceSettings.h"
#include "ioModule.h"

class CANManager {
private:
    static CANManager* instance;
    uint8_t deviceId;
    bool initialized;
    
    CANManager() : initialized(false) {}
    void handleMessage(const CANMessage& frame);

public:
    static CANManager& getInstance() {
        if (instance == nullptr) {
            instance = new CANManager();
        }
        return *instance;
    }

    bool begin(gpio_num_t rxPin, gpio_num_t txPin);
    void update();
    
    // Send commands
    bool requestInputState(uint8_t targetDeviceId, uint8_t inputNumber);
    bool setOutputState(uint8_t targetDeviceId, uint8_t outputNumber, bool state);
    bool sendHeartbeat();
    
    // Response handlers
    bool sendInputStateResponse(uint8_t targetDeviceId, uint8_t inputNumber, bool state);
    bool sendOutputStateResponse(uint8_t targetDeviceId, uint8_t outputNumber, bool state);
};