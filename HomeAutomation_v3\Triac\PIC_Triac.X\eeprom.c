#include "eeprom.h"
#include "config.h"

static uint16_t lastUsedBlock = 0xFFFF;

// Base EEPROM Functions
uint8_t EEPROM_Read(uint16_t address) {
    NVMADRL = (uint8_t)(address & 0xFF);
    NVMADRH = (uint8_t)(address >> 8);
    NVMADRU = 0x31;

    //Enable read
    NVMCON1bits.RD = 1;

    return NVMDATL;
}

void EEPROM_Write(uint16_t address, uint8_t data) {
    NVMADRL = (uint8_t)(address & 0xFF);
    NVMADRH = (uint8_t)(address >> 8);
    NVMADRU = 0x31;
    NVMDATL = data;
    
    //Enable writes
    NVMCON0bits.NVMEN = 1;
    
    //Disable interrupts
    INTCONbits.GIE = 0;
    
    //Required Sequence
    NVMCON2 = 0x55;
    NVMCON2 = 0xAA;
    NVMCON1bits.WR = 1;
    
    while (NVMCON1bits.WR) {
        //CLRWDT();
    }
    
    //Enable interrupts
    INTCONbits.GIE = 1;
    
    //Disable writes
    NVMCON0bits.NVMEN = 0;
}

uint8_t calculateChecksum(uint16_t data) {
    uint8_t checksum = 0;
    checksum ^= (uint8_t)(data & 0xFF);
    checksum ^= (uint8_t)(data >> 8);
    return checksum;
}

uint16_t findLatestBlock(void) {
    uint16_t latestValidBlock = 0xFFFF;
    uint8_t latestMagicNum = 0;
    
    // Search through all blocks
    for(uint16_t block = 0; block < EEPROM_SIZE; block += EEPROM_BLOCK_SIZE) {
        uint16_t outputs = ((uint16_t)EEPROM_Read(block + 1) << 8) | (uint16_t)EEPROM_Read(block);
        uint8_t storedChecksum = EEPROM_Read(block + 2);
        uint8_t magicNum = EEPROM_Read(block + 3);
        
        if(magicNum == MAGIC_NUMBER && storedChecksum == calculateChecksum(outputs)) {
            latestValidBlock = block;
        }
    }
    
    return latestValidBlock;
}

void saveOutputStates(void) {
    uint16_t lastValidBlock = findLatestBlock();
    uint16_t nextBlock;
    
    if (lastValidBlock == 0xFFFF) {
        nextBlock = 0;
    } else {
        nextBlock = lastValidBlock + EEPROM_BLOCK_SIZE;
        if (nextBlock >= (EEPROM_SIZE - EEPROM_BLOCK_SIZE)) {  // Fix: Check if we have space for full block
            nextBlock = 0;  // Wrap around to beginning
        }
    }
    
    // Before writing new block, invalidate the old one first
    if (lastValidBlock != 0xFFFF) {
        EEPROM_Write(lastValidBlock + 3, EEPROM_INVALID_MARKER);
    }
    
    uint16_t outputs = readOutputs();
    uint8_t checksum = calculateChecksum(outputs);
    
    // Write new block
    EEPROM_Write(nextBlock, outputs & 0xFF);
    EEPROM_Write(nextBlock + 1, outputs >> 8);
    EEPROM_Write(nextBlock + 2, checksum);
    EEPROM_Write(nextBlock + 3, MAGIC_NUMBER);
    
    lastUsedBlock = nextBlock;
}

uint16_t loadOutputStates(void) {
    uint16_t address = findLatestBlock();
    
    if (address == 0xFFFF) {
        return 0; // No valid data found, return default state
    }
    
    uint16_t outputs = ((uint16_t)EEPROM_Read(address + 1) << 8) | 
                      (uint16_t)EEPROM_Read(address);
    
    return outputs;
}

void restoreOutputStates(void) {
    // Load saved states on startup
    uint16_t savedStates = loadOutputStates();
    
    for(uint8_t i = 0; i < 16; i++) {
        writeOutput(i, (savedStates >> i) & 0x01);
    }
}


