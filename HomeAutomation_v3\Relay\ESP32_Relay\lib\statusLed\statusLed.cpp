#include "StatusLed.h"

int StatusLed::_ledPin;
StatusLed::Pattern StatusLed::_currentPattern;
unsigned long StatusLed::_previousMillis = 0;
bool StatusLed::_ledState = false;
unsigned long StatusLed::_onTime = 0;
unsigned long StatusLed::_offTime = 0;

void StatusLed::begin(int ledPin) {
    _ledPin = ledPin;
    pinMode(_ledPin, OUTPUT);
    setPattern(BOOT);  // Default pattern
}

void StatusLed::setPattern(Pattern pattern) {
    _currentPattern = pattern;
    _previousMillis = millis();

    switch (pattern) {
        case IDLE:
            _onTime = 10000;
            _offTime = 100;
            break;
        case BOOT:
            _onTime = 250; 
            _offTime = 250;
            break;
        case INPUT_CHANGE:
            _onTime = 1; 
            _offTime = 100;
            break;
        case UPDATE:
            _onTime = 100; 
            _offTime = 100;
            break;
        default:
            _onTime = 1000;
            _offTime = 1000;
            break;
    }
}

void StatusLed::update() {
    unsigned long currentMillis = millis();

    if (_ledState && (currentMillis - _previousMillis >= _onTime)) {
        _ledState = false;
        _previousMillis = currentMillis;
        digitalWrite(_ledPin, LOW);
    }
    else if (!_ledState && (currentMillis - _previousMillis >= _offTime)) {
        _ledState = true;
        _previousMillis = currentMillis;
        digitalWrite(_ledPin, HIGH);
    }
}

void StatusLed::toggle() {
    if(_ledState == false) {
        digitalWrite(_ledPin, HIGH);
        _ledState = true;
    } else {
        _ledState = false;
        digitalWrite(_ledPin, LOW);
    }
}