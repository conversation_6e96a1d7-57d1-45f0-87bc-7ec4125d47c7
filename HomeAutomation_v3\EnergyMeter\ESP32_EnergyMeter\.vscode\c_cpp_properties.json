//
// !!! WARNING !!! AUTO-GENERATED FILE!
// PLEASE DO NOT MODIFY IT AND USE "platformio.ini":
// https://docs.platformio.org/page/projectconf/section_env_build.html#build-flags
//
{
    "configurations": [
        {
            "name": "PlatformIO",
            "includePath": [
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Wire/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/WiFi/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Network/src",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/webServer",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Update/src",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/homeAssistant",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/PubSubClient",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/statusLed",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/ioModule",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/eventManager",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/ethernet",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SPI/src",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/deviceSettings",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Preferences/src",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/.pio/libdeps/esp32dev/ACAN_ESP32/src",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/.pio/libdeps/esp32dev/DallasTemperature",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/.pio/libdeps/esp32dev/OneWire@src-7394625c00c7ad4e547cedddf6448675",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/newlib/platform_include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/config/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/config/include/freertos",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/config/xtensa/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/FreeRTOS-Kernel/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/FreeRTOS-Kernel/portable/xtensa/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/esp_additions/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/include/soc",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/include/soc/esp32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/dma/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/ldo/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/port/esp32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/port/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/heap/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/log/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/soc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/soc/esp32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/soc/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/hal/platform_port/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/hal/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/hal/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_rom/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_rom/include/esp32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_rom/esp32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_common/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_system/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_system/port/soc",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_system/port/include/private",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/xtensa/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/xtensa/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/xtensa/deprecated_include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_timer/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/include/apps",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/include/apps/sntp",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/lwip/src/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/freertos/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/esp32xx/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/esp32xx/include/arch",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/esp32xx/include/sys",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro/third_party/gemmlowp",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro/third_party/flatbuffers/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro/third_party/ruy",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro/third_party/kissfft",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp32-camera/driver/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp32-camera/conversions/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/deprecated",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/i2c/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/touch_sensor/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/twai/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/touch_sensor/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_pm/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_ringbuf/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_gpio/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_pcnt/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_gptimer/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_spi/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_mcpwm/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_ana_cmpr/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_i2s/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_sdmmc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/sdmmc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_sdspi/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_sdio/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_dac/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_rmt/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_tsens/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_sdm/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_i2c/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_uart/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/vfs/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_ledc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_parlio/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_usb_serial_jtag/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/lib",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/lib/dnssd",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/OpenThread",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/third_party/nlfaultinjection/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/third_party/nlassert/repo/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/third_party/nlio/repo/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/zzz_generated/app-common",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp-idf/espressif__esp_matter",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter/utils",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter_bridge",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter_console",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter/zap_common",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/ESP32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/ESP32/bluedroid",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/ESP32/nimble",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/ESP32/route_hook",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_eth/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_event/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/include/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/osi/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/api/include/api",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/btc/profile/esp/blufi/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/btc/profile/esp/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/hci_log/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/host/bluedroid/api/include/api",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/common/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/common/tinycrypt/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/core",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/core/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/core/storage",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/btc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/models/common/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/models/client/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/models/server/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/api/core/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/api/models/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/api",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/lib/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/v1.1/api/core/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/v1.1/api/models/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/v1.1/btc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/porting/ext/tinycrypt/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_wifi/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_wifi/include/local",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_wifi/wifi_apps/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_wifi/wifi_apps/nan_app/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_phy/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_phy/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_netif/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/port/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/library",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/esp_crt_bundle/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/3rdparty/everest/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/3rdparty/p256-m",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/3rdparty/p256-m/p256-m",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/fatfs/diskio",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/fatfs/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/fatfs/vfs",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wear_levelling/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_partition/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/app_update/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bootloader_support/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bootloader_support/bootloader_flash/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_app_format/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_bootloader_format/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/console",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_vfs_console/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/nvs_flash/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/spi_flash/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_secure_cert_mgr/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/efuse/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/efuse/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__json_parser/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__jsmn/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/spiffs/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_http_client/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__json_generator/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/json/cJSON",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__mdns/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_encrypted_img/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_insights/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_diagnostics/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_mm/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/pthread/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/app_trace/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wpa_supplicant/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wpa_supplicant/port/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wpa_supplicant/esp_supplicant/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_coex/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/unity/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/unity/unity/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/cmock/CMock/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/http_parser",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp-tls",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp-tls/esp-tls-crypto",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_adc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_adc/interface",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_adc/esp32/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_adc/deprecated/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_isp/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_cam/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_cam/interface",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_jpeg/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_ppa/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_gdbstub/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hid/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/tcp_transport/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_http_server/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_https_ota/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_https_server/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_psram/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_lcd/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_lcd/interface",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protobuf-c/protobuf-c",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/include/common",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/include/security",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/include/transports",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/include/crypto/srp6a",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/proto-c",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_local_ctrl/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espcoredump/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espcoredump/include/port/xtensa",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/idf_test/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/idf_test/include/esp32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/ieee802154/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mqtt/esp-mqtt/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/nvs_sec_provider/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/perfmon/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/ulp/ulp_common/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/ulp/ulp_fsm/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/ulp/ulp_fsm/include/esp32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wifi_provisioning/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-nn/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-nn/src/common",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__rmaker_common/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__cbor/port/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_diag_data_store/src/rtc_store",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_diag_data_store/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/chmorgan__esp-libhelix-mp3/libhelix-mp3/pub",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-modbus/freemodbus/common/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__libsodium/libsodium/src/libsodium/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__libsodium/port_include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/dotprod/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/support/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/support/mem/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/hann/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/blackman/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/blackman_harris/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/nuttall/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/flat_top/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/iir/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/fir/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/add/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/sub/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/mul/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/addc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/mulc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/sqrt/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/mul/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/add/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/addc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/mulc/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/sub/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/fft/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/dct/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/conv/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/common/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/mul/test/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/kalman/ekf/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_modem/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_schedule/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__network_provisioning/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-serial-flasher/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-serial-flasher/port",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_rcp_update/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_rainmaker/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__qrcode/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/joltwallet__littlefs/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/fb_gfx/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/dio_qspi/include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/cores/esp32",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/variants/esp32",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/.pio/libdeps/esp32dev/OneWire",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ArduinoOTA/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/AsyncUDP/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/BLE/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/BluetoothSerial/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/DNSServer/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/EEPROM/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESP32/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESP_I2S/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESP_NOW/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESP_SR/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESPmDNS/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Ethernet/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/FFat/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/FS/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/HTTPClient/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/HTTPUpdate/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/HTTPUpdateServer/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Insights/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/LittleFS/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Matter/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/NetBIOS/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/NetworkClientSecure/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/OpenThread/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/PPP/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/RainMaker/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SD/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SD_MMC/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SPIFFS/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SimpleBLE/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/TFLiteMicro/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Ticker/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/USB/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/WebServer/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/WiFiProv/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Zigbee/src",
                ""
            ],
            "browse": {
                "limitSymbolsToIncludedHeaders": true,
                "path": [
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Wire/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/WiFi/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Network/src",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/webServer",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Update/src",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/homeAssistant",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/PubSubClient",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/statusLed",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/ioModule",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/eventManager",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/ethernet",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SPI/src",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/lib/deviceSettings",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Preferences/src",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/.pio/libdeps/esp32dev/ACAN_ESP32/src",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/.pio/libdeps/esp32dev/DallasTemperature",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/.pio/libdeps/esp32dev/OneWire@src-7394625c00c7ad4e547cedddf6448675",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/newlib/platform_include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/config/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/config/include/freertos",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/config/xtensa/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/FreeRTOS-Kernel/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/FreeRTOS-Kernel/portable/xtensa/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/freertos/esp_additions/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/include/soc",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/include/soc/esp32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/dma/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/ldo/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/port/esp32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hw_support/port/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/heap/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/log/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/soc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/soc/esp32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/soc/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/hal/platform_port/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/hal/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/hal/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_rom/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_rom/include/esp32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_rom/esp32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_common/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_system/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_system/port/soc",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_system/port/include/private",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/xtensa/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/xtensa/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/xtensa/deprecated_include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_timer/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/include/apps",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/include/apps/sntp",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/lwip/src/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/freertos/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/esp32xx/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/esp32xx/include/arch",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/lwip/port/esp32xx/include/sys",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro/third_party/gemmlowp",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro/third_party/flatbuffers/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro/third_party/ruy",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-tflite-micro/third_party/kissfft",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp32-camera/driver/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp32-camera/conversions/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/deprecated",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/i2c/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/touch_sensor/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/twai/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/driver/touch_sensor/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_pm/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_ringbuf/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_gpio/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_pcnt/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_gptimer/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_spi/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_mcpwm/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_ana_cmpr/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_i2s/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_sdmmc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/sdmmc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_sdspi/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_sdio/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_dac/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_rmt/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_tsens/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_sdm/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_i2c/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_uart/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/vfs/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_ledc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_parlio/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_usb_serial_jtag/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/lib",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/lib/dnssd",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/OpenThread",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/third_party/nlfaultinjection/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/third_party/nlassert/repo/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/third_party/nlio/repo/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/zzz_generated/app-common",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp-idf/espressif__esp_matter",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter/utils",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter_bridge",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter_console",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_matter/zap_common",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/ESP32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/ESP32/bluedroid",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/ESP32/nimble",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_matter/connectedhomeip/connectedhomeip/src/platform/ESP32/route_hook",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_eth/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_event/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/include/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/osi/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/api/include/api",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/btc/profile/esp/blufi/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/btc/profile/esp/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/common/hci_log/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/host/bluedroid/api/include/api",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/common/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/common/tinycrypt/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/core",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/core/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/core/storage",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/btc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/models/common/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/models/client/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/models/server/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/api/core/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/api/models/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/api",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/lib/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/v1.1/api/core/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/v1.1/api/models/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/esp_ble_mesh/v1.1/btc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bt/porting/ext/tinycrypt/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_wifi/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_wifi/include/local",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_wifi/wifi_apps/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_wifi/wifi_apps/nan_app/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_phy/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_phy/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_netif/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/port/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/library",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/esp_crt_bundle/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/3rdparty/everest/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/3rdparty/p256-m",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mbedtls/mbedtls/3rdparty/p256-m/p256-m",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/fatfs/diskio",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/fatfs/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/fatfs/vfs",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wear_levelling/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_partition/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/app_update/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bootloader_support/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/bootloader_support/bootloader_flash/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_app_format/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_bootloader_format/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/console",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_vfs_console/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/nvs_flash/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/spi_flash/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_secure_cert_mgr/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/efuse/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/efuse/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__json_parser/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__jsmn/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/spiffs/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_http_client/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__json_generator/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/json/cJSON",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__mdns/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_encrypted_img/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_insights/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_diagnostics/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_mm/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/pthread/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/app_trace/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wpa_supplicant/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wpa_supplicant/port/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wpa_supplicant/esp_supplicant/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_coex/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/unity/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/unity/unity/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/cmock/CMock/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/http_parser",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp-tls",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp-tls/esp-tls-crypto",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_adc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_adc/interface",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_adc/esp32/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_adc/deprecated/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_isp/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_cam/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_cam/interface",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_jpeg/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_driver_ppa/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_gdbstub/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_hid/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/tcp_transport/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_http_server/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_https_ota/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_https_server/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_psram/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_lcd/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_lcd/interface",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protobuf-c/protobuf-c",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/include/common",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/include/security",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/include/transports",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/include/crypto/srp6a",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/protocomm/proto-c",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/esp_local_ctrl/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espcoredump/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espcoredump/include/port/xtensa",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/idf_test/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/idf_test/include/esp32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/ieee802154/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/mqtt/esp-mqtt/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/nvs_sec_provider/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/perfmon/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/ulp/ulp_common/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/ulp/ulp_fsm/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/ulp/ulp_fsm/include/esp32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/wifi_provisioning/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-nn/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-nn/src/common",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__rmaker_common/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__cbor/port/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_diag_data_store/src/rtc_store",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_diag_data_store/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/chmorgan__esp-libhelix-mp3/libhelix-mp3/pub",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-modbus/freemodbus/common/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__libsodium/libsodium/src/libsodium/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__libsodium/port_include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/dotprod/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/support/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/support/mem/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/hann/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/blackman/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/blackman_harris/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/nuttall/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/windows/flat_top/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/iir/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/fir/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/add/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/sub/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/mul/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/addc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/mulc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/math/sqrt/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/mul/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/add/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/addc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/mulc/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/sub/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/fft/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/dct/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/conv/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/common/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/matrix/mul/test/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/kalman/ekf/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_modem/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_schedule/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__network_provisioning/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-serial-flasher/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp-serial-flasher/port",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_rcp_update/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__esp_rainmaker/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/espressif__qrcode/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/joltwallet__littlefs/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/include/fb_gfx/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32-libs/esp32/dio_qspi/include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/cores/esp32",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/variants/esp32",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/ESP32_EnergyMeter/.pio/libdeps/esp32dev/OneWire",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ArduinoOTA/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/AsyncUDP/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/BLE/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/BluetoothSerial/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/DNSServer/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/EEPROM/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESP32/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESP_I2S/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESP_NOW/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESP_SR/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/ESPmDNS/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Ethernet/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/FFat/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/FS/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/HTTPClient/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/HTTPUpdate/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/HTTPUpdateServer/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Insights/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/LittleFS/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Matter/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/NetBIOS/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/NetworkClientSecure/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/OpenThread/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/PPP/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/RainMaker/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SD/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SD_MMC/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SPIFFS/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/SimpleBLE/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/TFLiteMicro/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Ticker/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/USB/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/WebServer/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/WiFiProv/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@src-36b172f5f4482da14324b9ce108a8e04/libraries/Zigbee/src",
                    ""
                ]
            },
            "defines": [
                "PLATFORMIO=60118",
                "ARDUINO_ESP32_DEV",
                "CORE_DEBUG_LEVEL=1",
                "ESP32_ARDUINO_LIB_BUILDER",
                "ESP_PLATFORM",
                "IDF_VER=\"v5.3.2-584-g489d7a2b3a-dirty\"",
                "MBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\"",
                "MD5_ENABLED=1",
                "SERIAL_FLASHER_BOOT_HOLD_TIME_MS=50",
                "SERIAL_FLASHER_RESET_HOLD_TIME_MS=100",
                "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE",
                "SOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ",
                "UNITY_INCLUDE_CONFIG_H",
                "_GLIBCXX_HAVE_POSIX_SEMAPHORE",
                "_GLIBCXX_USE_POSIX_SEMAPHORE",
                "_GNU_SOURCE",
                "_POSIX_READER_WRITER_LOCKS",
                "TF_LITE_STATIC_MEMORY",
                "CHIP_CONFIG_SOFTWARE_VERSION_NUMBER=0",
                "CHIP_MINMDNS_DEFAULT_POLICY=1",
                "CHIP_MINMDNS_USE_EPHEMERAL_UNICAST_PORT=0",
                "CHIP_MINMDNS_HIGH_VERBOSITY=0",
                "CHIP_DNSSD_DEFAULT_MINIMAL=1",
                "ARDUINO_ARCH_ESP32",
                "CHIP_HAVE_CONFIG_H",
                "ESP32=ESP32",
                "F_CPU=240000000L",
                "ARDUINO=10812",
                "ARDUINO_VARIANT=\"esp32\"",
                "ARDUINO_BOARD=\"Espressif ESP32 Dev Module\"",
                "ARDUINO_PARTITION_default",
                ""
            ],
            "cStandard": "gnu17",
            "cppStandard": "gnu++17",
            "compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe",
            "compilerArgs": [
                "-mlongcalls",
                "-mdisable-hardware-atomics",
                ""
            ]
        }
    ],
    "version": 4
}
