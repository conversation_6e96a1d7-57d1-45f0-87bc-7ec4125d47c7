import os
import gzip
from pathlib import Path

#def compress_and_convert(source, target, env):
def compress_and_convert():
    # Define the source and target files
    source_file = Path("data/index.html")
    header_file = Path("lib/webServer/index_html_gz.h")

    # Compress the index.html to a byte array
    with open(source_file, 'rb') as f_in:
        compressed_data = gzip.compress(f_in.read())

    # Write the compressed data to a header file
    with open(header_file, 'w') as f_out:
        f_out.write("const unsigned char index_html_gz[] = {")
        for i, byte in enumerate(compressed_data):
            if i % 12 == 0:
                f_out.write("\n  ")
            f_out.write(f"0x{byte:02x}, ")
        f_out.write("\n};\n")
        f_out.write(f"const unsigned int index_html_gz_len = {len(compressed_data)};\n")

    # Ensure the header file is included in the build
    #env.Append(CPPPATH=[os.path.join(env['PROJECT_DIR'], 'src')])

compress_and_convert()
# Register the function with PlatformIO
#Import("env")
#env.AddPreAction("build", compress_and_convert)
