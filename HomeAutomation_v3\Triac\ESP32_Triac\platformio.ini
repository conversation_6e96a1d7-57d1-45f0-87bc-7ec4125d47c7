; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = https://github.com/pioarduino/platform-espressif32/releases/download/stable/platform-espressif32.zip
board = esp32dev
framework = arduino



monitor_speed = 115200

lib_deps =     
    https://github.com/PaulStoffregen/OneWire.git
    https://github.com/milesburton/Arduino-Temperature-Control-Library.git
    https://github.com/pierremolinaro/acan-esp32.git

;Original HA Library, but we have extended it
;https://github.com/dawid<PERSON><PERSON><PERSON><PERSON>/arduino-home-assistant.git

; Partition table for 4mb flash
;https://esp32.jgarrettcorbin.com/
;board_build.partitions = partitions.csv

build_flags = -DCORE_DEBUG_LEVEL=1 -I include

;upload_protocol = custom
;upload_command = python.exe espota.py -i ************* -p 80 -f .pio/build/esp32dev/firmware.bin

;Files in DATA dir can be uploaded to the SPIFFS, but its not an easy task to do and we have to do two separate OTA updates - 1 for code and 1 for static files
;That's why we have the pre_build.py
;The script is getting index.html, gzip it and put the bytes into a header file , that is later inclued in the webserver
extra_scripts = pre:pre_build.py
