#define Serial Serial3
#include <Arduino.h>
#include <avr/wdt.h>
#include "emonLibDB.h"


/*

| Command Name    | CMD ID | Payload Description                     | Total Length |
| --------------- | ------ | --------------------------------------- | ------------ |
| `DataLogPeriod` | `0x01` | `float time`                            | 5 bytes      |
| `VoltInputs`    | `0x02` | `int inputs, float cal, float phaseCal` | 10 bytes     |
| `CurrentInputs` | `0x03` | `int inputs, float cal, float phaseCal` | 10 bytes     |
| `PowerInputs`   | `0x04` | `int curID, int voltID`                 | 3 bytes      |
| `Init`          | `0x05` | none                                    | 1 byte       |
| `Status`        | `0x06` | none                                    | 1 byte       |
| `GetVolts`      | `0x07` | none                                    | 1 byte       |
| `GetCurrent`    | `0x08` | none                                    | 1 byte       |
| `Version`       | `0x09` | none                                    | 1 byte       |
| `Debug`         | `0x10` | none                                    | 1 byte       |
| `Reset`         | `0x1A` | none                                    | 1 byte       |
| `ReportAllSettings` | `0x1B` | none                                    | 1 byte       |

*/

#define START_BYTE  0xAA
#define ACK_BYTE    '#'
#define ERROR_BYTE  '@'
#define TIMEOUT_MS  1000

#define BUFFER_SIZE 64  // Increased buffer size

const byte ESPInterruptPin = PIN_PB2;

char APP_VERSION  = 1;

char rxBuffer[BUFFER_SIZE];
char rxBufferSize = 0;
char rxCommandReady = 0;

uint8_t bufferPos = 0;
bool receiving = false;
uint8_t expectedLen = 0;
unsigned long lastByteTime = 0;

bool emonInit = false;

// Calculate CRC for better error detection
uint8_t calculateCRC(const uint8_t* data, uint8_t length) {
    uint8_t crc = 0;
    for (uint8_t i = 0; i < length; i++) {
        crc = crc ^ data[i];
    }
    return crc;
}

void sendResponse(uint8_t cmd, const uint8_t* payload, uint8_t payloadLen) {
    // Prepare complete packet in buffer
    uint8_t packet[BUFFER_SIZE];
    uint8_t packetLen = 0;
    
    // Add header
    packet[packetLen++] = START_BYTE;
    packet[packetLen++] = payloadLen + 2;  // +2 for command and CRC
    packet[packetLen++] = cmd;
    
    // Add payload if any
    if (payloadLen > 0) {
        memcpy(&packet[packetLen], payload, payloadLen);
        packetLen += payloadLen;
    }
    
    // Calculate CRC for entire packet except CRC byte
    uint8_t crc = calculateCRC(packet, packetLen);
    packet[packetLen++] = crc;
    
    // Send complete packet
    Serial.write(packet, packetLen);
}

void setDatalogPeriod(uint8_t* payload) {
  float time;
  memcpy(&time, payload, 4);
  EmonLibDB_datalogPeriod(time);
  Serial.print(ACK_BYTE);
}

void setVoltageInput(uint8_t* payload) {
  int voltageInput = payload[0];
  float voltageCal, voltagePhase;
  memcpy(&voltageCal, &payload[1], 4);
  memcpy(&voltagePhase, &payload[5], 4);

  if(emonInit) {
    EmonLibDB_reCalibrate_vInput(voltageInput, voltageCal, voltagePhase);
  } else {
    EmonLibDB_set_vInput(voltageInput, voltageCal, voltagePhase);
  }
  Serial.print(ACK_BYTE);
}

void setCurrentInput(uint8_t* payload) {
  int currentInput = payload[0];
  float currentCal, currentPhase;
  memcpy(&currentCal, &payload[1], 4);
  memcpy(&currentPhase, &payload[5], 4);

  if(emonInit) {
    EmonLibDB_reCalibrate_cInput(currentInput, currentCal, currentPhase);
  } else {
    EmonLibDB_set_cInput(currentInput, currentCal, currentPhase);
  }
  Serial.print(ACK_BYTE);
}

void setPowertInput(uint8_t* payload) {
    if (payload[0] > 12 || payload[1] > 12) {
        Serial.print(ERROR_BYTE);
        return;
    }
    
    int input1 = payload[0];
    int input2 = payload[1];  // Fixed: was incorrectly using payload[0]
    
    EmonLibDB_set_pInput(input1, input2);
    EmonLibDB_setWattHour(input1, 0);
    Serial.print(ACK_BYTE);
}

void initEmonLib() {
  if(!emonInit) {
    EmonLibDB_Init();
    emonInit = true;
    Serial.print(ACK_BYTE);
  } else {
    Serial.print(ERROR_BYTE);
  }
}

void reportStatus() {
  Serial.print(ACK_BYTE);

  uint8_t payload[1];
  payload[0] = emonInit;
  sendResponse(0x06, payload, 1);
}

void reportVoltage() {
  Serial.print(ACK_BYTE);

  uint8_t payload[12];
  double v1 = EmonLibDB_getVrms(1);
  double v2 = EmonLibDB_getVrms(2);
  double v3 = EmonLibDB_getVrms(3);
  
  memcpy(&payload[0], &v1, 4);
  memcpy(&payload[4], &v2, 4);
  memcpy(&payload[8], &v3, 4);
  
  sendResponse(0x07, payload, 12);
}

void reportPower(uint8_t* payload) {
  Serial.print(ACK_BYTE);

  //Clear the interrupt pin
  digitalWrite(ESPInterruptPin, LOW);

  int powerChannel = payload[0];

  uint8_t payloadResponse[16];
  double irms = EmonLibDB_getIrms(powerChannel);
  int16_t realPower = EmonLibDB_getRealPower(powerChannel);
  int16_t apparentPower = EmonLibDB_getApparentPower(powerChannel);
  int32_t wattHour = EmonLibDB_getWattHour(powerChannel);
  double pf = EmonLibDB_getPF(powerChannel);
  
  memcpy(&payloadResponse[0], &irms, 4);
  memcpy(&payloadResponse[4], &realPower, 2);
  memcpy(&payloadResponse[6], &apparentPower, 2);
  memcpy(&payloadResponse[8], &wattHour, 4);
  memcpy(&payloadResponse[12], &pf, 4);
  
  sendResponse(0x08, payloadResponse, 16);  
}

void reportVersion() {
  Serial.print(ACK_BYTE);

  uint8_t payload[1];
  payload[0] = APP_VERSION;
  sendResponse(0x09, payload, 1);
}

void reportDebug() {
  Serial.print(ACK_BYTE);

  Serial.print(EmonLibDB_acPresent()?"AC present ":"AC missing ");
  Serial.print("V1 = ");Serial.println(EmonLibDB_getVrms(1));
  Serial.print("V2 = ");Serial.println(EmonLibDB_getVrms(2));
  Serial.print("V3 = ");Serial.println(EmonLibDB_getVrms(3));
  Serial.print("F = ");Serial.println(EmonLibDB_getLineFrequency());

  for (uint8_t ch=1; ch<=12; ch++)
  {
    if (EmonLibDB_getCinputInUse(ch))
    {
      Serial.print("Ch ");Serial.println(ch);
      Serial.print(" I=");Serial.println(EmonLibDB_getIrms(ch),3);
      Serial.print(" W=");Serial.println(EmonLibDB_getRealPower(ch));
      Serial.print(" VA=");Serial.println(EmonLibDB_getApparentPower(ch));
      Serial.print(" Wh=");Serial.println(EmonLibDB_getWattHour(ch));
      Serial.print(" pf=");Serial.println(EmonLibDB_getPF(ch),4);      
      Serial.println();
      delay(20);
    }
  }   
}

void reportAllSettings() {
    Serial.print(ACK_BYTE);
    
    // Calculate total payload size
    // 3 voltage inputs * (1 byte ID + 4 bytes cal + 4 bytes phase) = 27 bytes
    // 12 current inputs * (1 byte ID + 4 bytes cal + 4 bytes phase) = 108 bytes
    // 12 power inputs * (1 byte ID + 1 byte voltID) = 24 bytes
    // Total: 159 bytes
    
    uint8_t payload[159];
    uint8_t pos = 0;
    return; 
    /*
    // Report voltage inputs
    for (uint8_t i = 1; i <= 3; i++) {
        float cal, phase;
        EmonLibDB_get_vCalibration(i, &cal, &phase);
        
        payload[pos++] = i;  // Input ID
        memcpy(&payload[pos], &cal, 4);    // Calibration value
        pos += 4;
        memcpy(&payload[pos], &phase, 4);  // Phase value
        pos += 4;
    }
    
    // Report current inputs
    for (uint8_t i = 1; i <= 12; i++) {
        float cal, phase;
        EmonLibDB_get_cCalibration(i, &cal, &phase);
        
        payload[pos++] = i;  // Input ID
        memcpy(&payload[pos], &cal, 4);    // Calibration value
        pos += 4;
        memcpy(&payload[pos], &phase, 4);  // Phase value
        pos += 4;
    }
    
    // Report power inputs
    for (uint8_t i = 1; i <= 12; i++) {
        uint8_t voltID = EmonLibDB_get_pVoltID(i);
        payload[pos++] = i;      // Power input ID
        payload[pos++] = voltID; // Voltage input ID
    }
    
    sendResponse(0x1B, payload, 159);
    */
}

void clearInterrupt() {
  digitalWrite(ESPInterruptPin, LOW);
  Serial.print(ACK_BYTE);
}

void handleCommand(uint8_t cmd, uint8_t* payload) {
  //Serial.print("CMD:"); Serial.print(cmd); Serial.print(" ");
  switch (cmd) {
    case 0x01:
      setDatalogPeriod(payload);
      break;
      
    case 0x02: 
      setVoltageInput(payload);
      break;

    case 0x03: 
      setCurrentInput(payload);
      break;

    case 0x04:
      setPowertInput(payload);
      break;

    case 0x05: //Init
      initEmonLib();
      break;

    case 0x06: //Status
      reportStatus();
      break;

    case 0x07: //GetVolts
      reportVoltage();
      break;

    case 0x08: //GetCurrent
      reportPower(payload);
      break;

    case 0x09: //Version
      reportVersion();
      break;

    case 0x10: //Debug
      reportDebug();
      break;

    case 0x1A: //Reset
      Serial.print(ACK_BYTE);
      // Enable watchdog timer with shortest timeout (15ms)
      wdt_enable(WDT_PERIOD_8CLK_gc);
      // Wait for watchdog to reset the device
      while(1) {}
      
      break;

    case 0x1B: //Report All Settings
      reportAllSettings();
      break;

    case 0x1C: //Clear Interrupt
      clearInterrupt();
      break;

    default:
      Serial.print(ERROR_BYTE);
      break;
  }
}

void processByte(uint8_t byte) {
    unsigned long currentTime = millis();
    
    // Check for timeout
    if (receiving && (currentTime - lastByteTime > TIMEOUT_MS)) {
        //Reset the buffer
        receiving = false;
        bufferPos = 0;
    }
    
    lastByteTime = currentTime;
    
    if (!receiving) {
        if (byte == START_BYTE) {
            receiving = true;
            bufferPos = 0;
            rxBuffer[bufferPos++] = byte;
        }
        return;
    }

    // Check for overflow
    if (bufferPos >= BUFFER_SIZE) {
        receiving = false;
        bufferPos = 0;
        return;
    }

    // Store the byte
    rxBuffer[bufferPos++] = byte;

    // Package length
    if (bufferPos == 2) {
        expectedLen = byte + 2; // Add 2 for start byte and length byte
        if (expectedLen > BUFFER_SIZE) {
            receiving = false;
            bufferPos = 0;
            return;
        }
    }

    // Check for end of package
    if (bufferPos == expectedLen) {
        // Full packet received
        uint8_t cmd = rxBuffer[2];
        uint8_t receivedCRC = rxBuffer[expectedLen - 1];  // CRC is always the last byte
        
        // Calculate CRC for entire packet except CRC byte
        uint8_t calculatedCRC = calculateCRC(rxBuffer, expectedLen - 1);

        if (receivedCRC == calculatedCRC) {
            // Pass the payload (everything between command and CRC)
            handleCommand(cmd, &rxBuffer[3]);
        } else {
            Serial.print(ERROR_BYTE);
        }

        receiving = false;
        bufferPos = 0;
    }
}

void handleUART() {
  while (Serial.available()) {
    uint8_t byte = Serial.read();
    processByte(byte);
  }
}


void setup() 
{  

  pinMode(ESPInterruptPin, OUTPUT);
  digitalWrite(ESPInterruptPin, LOW);

  delay(50);

  Serial.begin(115200);
  Serial.print(ACK_BYTE); 
  //Serial.println("\nEmonTx4 EmonLibCM Continuous Monitoring Maximal Demo"); 
  //Serial.print("\nValues will be reported every ");  
  //Serial.print(EmonLibDB_getDatalogPeriod()); Serial.println(" seconds");
 
  /****************************************************************************
  *                                                                           *
  * Values for timing & calibration of the ADC                                *
  *                                                                           *
  *   Users in the 60 Hz world must change "cycles_per_second"                *
  *                                                                           *
  *   (These must come before the sensors are defined)                        *
  *                                                                           *
  ****************************************************************************/

  EmonLibDB_cyclesPerSecond(50);             // Nominal Line frequency - 50 Hz or 60 Hz
  EmonLibDB_minStartupCycles(10);            // number of cycles to let ADC run before starting first actual measurement
  EmonLibDB_datalogPeriod(9.8);              // period of readings in seconds - normal value for emoncms.org
  EmonLibDB_ADCCal(1.024);                   // ADC Reference voltage
  EmonLibDB_fCal(1.00);                      // Correction for processor clock tolerances

 
  /****************************************************************************
  *                                                                           *
  * Set the properties of the physical sensors                                *
  *                                                                           *
  ****************************************************************************/
 
  EmonLibDB_set_vInput(1, 200.0, 0.16);        // emonVS Input channel 1, voltage calibration 100, phase error 0.16°
  EmonLibDB_set_vInput(2, 200.0, 0.16);        // emonVS Input channel 2, voltage calibration 100, phase error 0.16°
  EmonLibDB_set_vInput(3, 200.0, 0.16);        // emonVS Input channel 3, voltage calibration 100, phase error 0.16°
                                               //  (All 3 may be set, even if those inputs are unused)

  EmonLibDB_set_cInput(1, 50.0, 0.3);         // emonTx4 Current/power Input channel 1, 0.3° phase error for 100 A CT
  EmonLibDB_set_cInput(2, 50.0, 0.3);         // emonTx4 Current/power Input channel 2, 0.3° phase error for  50 A CT
  EmonLibDB_set_cInput(3, 50.0, 0.3);         // emonTx4 Current/power Input channel 3, 0.3° phase error for  20 A CT
  EmonLibDB_set_cInput(4, 50.0, 0.3);         //  (likewise for current/power Inputs 4 - 12)
  EmonLibDB_set_cInput(5, 50.0, 0.3);
  EmonLibDB_set_cInput(6, 50.0, 0.3);
  EmonLibDB_set_cInput(7, 50.0, 0.3);
  EmonLibDB_set_cInput(8, 50.0, 0.3);
  EmonLibDB_set_cInput(9, 50.0, 0.3);
  EmonLibDB_set_cInput(10, 50.0, 0.3);
  EmonLibDB_set_cInput(11, 50.0, 0.3);
  EmonLibDB_set_cInput(12, 50.0, 0.3);


  /****************************************************************************
  *                                                                           *
  * Link voltage and current sensors to define the power                      *
  *  & energy measurements                                                    *
  *                                                                           *
  * For best precision and performance, include only the following lines      *
  *    that apply to current/power inputs being used.                         *
  *                                                                           *
  ****************************************************************************/

  EmonLibDB_set_pInput(1, 1);                  // CT1, V1
  EmonLibDB_set_pInput(2, 1);                  // CT2, V2 (etc)
  EmonLibDB_set_pInput(3, 1);
  EmonLibDB_set_pInput(4, 1);  
  EmonLibDB_set_pInput(5, 1);
  EmonLibDB_set_pInput(6, 1);
  EmonLibDB_set_pInput(7, 1);                  // CT7, V1  
  EmonLibDB_set_pInput(8, 1);                  // CT8, V2
  EmonLibDB_set_pInput(9, 1);                  // CT9, V3 (etc)
  EmonLibDB_set_pInput(10, 1);  
  EmonLibDB_set_pInput(11, 1);
  EmonLibDB_set_pInput(12, 1);


  /* How to measure Line-Line loads 
      - either 3-phase or single phase, split phase:
  */
/*
  EmonLibDB_set_pInput(1, 1, 2);               // CT1 between V1 & V2    
  EmonLibDB_set_pInput(2, 2, 3);               // CT2 between V2 & V3
  EmonLibDB_set_pInput(3, 3, 1);               // CT3 between V3 & V1  (etc)  
  EmonLibDB_set_pInput(4, 1, 2);  
  EmonLibDB_set_pInput(5, 2, 3);
  EmonLibDB_set_pInput(6, 3, 1);

  EmonLibDB_set_pInput(7, 1, 2);  
  EmonLibDB_set_pInput(8, 2, 3);
  EmonLibDB_set_pInput(9, 3, 1);
  EmonLibDB_set_pInput(10, 1, 2);  
  EmonLibDB_set_pInput(11, 2, 3);
  EmonLibDB_set_pInput(12, 3, 1);
*/

  EmonLibDB_setPulseEnable(false);              // Enable counting on "Pulse" input
  EmonLibDB_setAnalogueEnable(false);          // Disable analogue input (cannot be used when the extender card is fitted.

  
  /****************************************************************************
  *                                                                           *
  * Pre-set Energy counters                                                   *
  *                                                                           *
  ****************************************************************************/
  //EmonLibDB_setWattHour(1, 0);                 // Wh counter set to zero
  //EmonLibDB_setWattHour(2, 0);                 //  (likewise for all 12 channels)

  EmonLibDB_Init();                            // Start continuous monitoring.
}

void loop()             
{

  handleUART();

  if (EmonLibDB_Ready())   
  {

    digitalWrite(ESPInterruptPin, HIGH);
    delay(1);
/*
    delay(100);

    Serial.print(EmonLibDB_acPresent()?"AC present ":"AC missing ");
    delay(50);

    Serial.print("V1 = ");Serial.print(EmonLibDB_getVrms(1));
    Serial.print(" V2 = ");Serial.print(EmonLibDB_getVrms(2));
    Serial.print(" V3 = ");Serial.print(EmonLibDB_getVrms(3));
    Serial.print(" f = ");Serial.println(EmonLibDB_getLineFrequency());
        
    
    for (uint8_t ch=1; ch<=12; ch++)
    {
      if (EmonLibDB_getCinputInUse(ch))
      {
        Serial.print("Ch ");Serial.print(ch);
        Serial.print(" I=");Serial.print(EmonLibDB_getIrms(ch),3);
        Serial.print(" W=");Serial.print(EmonLibDB_getRealPower(ch));
        Serial.print(" VA=");Serial.print(EmonLibDB_getApparentPower(ch));
        Serial.print(" Wh=");Serial.print(EmonLibDB_getWattHour(ch));
        Serial.print(" pf=");Serial.print(EmonLibDB_getPF(ch),4);      
        Serial.println();
        delay(20);
      }
    }   
    
    delay(50);
  */  
  }
}
