#include "picProgrammer.h"

PICProgrammer::PICProgrammer() : currentAddress(0), progressCallback(nullptr) {
}

PICProgrammer& PICProgrammer::getInstance() {
    static PICProgrammer instance;
    return instance;
}

void PICProgrammer::begin() {
    pinMode(PIC_MCLR, OUTPUT);
    pinMode(PIC_PGD, OUTPUT);
    pinMode(PIC_PGC, OUTPUT);
    
    setMCLR(true);
    setPGC(false);
    setPGD(false);
}

bool PICProgrammer::programHexFile(const uint8_t* hexData, size_t length, void (*progress)(int)) {
    progressCallback = progress;
    
    if (!enterProgrammingMode()) {
        return false;
    }
    
    if (!bulkErase()) {
        exitProgrammingMode();
        return false;
    }
    
    char line[100];
    uint32_t address;
    uint8_t data[16];
    uint8_t dataLength;
    int lineStart = 0;
    size_t totalBytes = 0;
    
    // First pass: count total bytes for progress calculation
    for(size_t i = 0; i < length; i++) {
        if(hexData[i] == '\n' || hexData[i] == '\r') {
            totalBytes += (i - lineStart);
            lineStart = i + 1;
        }
    }
    
    // Reset for actual programming
    lineStart = 0;
    size_t processedBytes = 0;
    
    for(size_t i = 0; i < length; i++) {
        if(hexData[i] == '\n' || hexData[i] == '\r' || i == length-1) {
            if(i > lineStart) {
                memcpy(line, &hexData[lineStart], i-lineStart);
                line[i-lineStart] = 0;
                
                if(parseHexLine(line, address, data, dataLength)) {
                    // Handle different memory regions
                    if(address >= PROGRAM_START && address <= PROGRAM_END) {
                        if(!loadAddressForProgMem(address)) {
                            exitProgrammingMode();
                            return false;
                        }
                        
                        for(int j = 0; j < dataLength; j += 2) {
                            uint16_t word = (data[j+1] << 8) | data[j];
                            
                            if(!loadDataForProgMem(word) || 
                               !beginProgramming() || 
                               !verifyProgMem(address + j/2, word)) {
                                exitProgrammingMode();
                                return false;
                            }
                            
                            if(!incrementAddress()) {
                                exitProgrammingMode();
                                return false;
                            }
                        }
                    }
                    else if(address >= CONFIG_START && address <= CONFIG_END) {
                        for(int j = 0; j < dataLength; j += 2) {
                            uint16_t word = (data[j+1] << 8) | data[j];
                            if(!loadConfiguration(address + j/2, word)) {
                                exitProgrammingMode();
                                return false;
                            }
                        }
                    }
                }
                
                processedBytes += (i - lineStart);
                if(progressCallback) {
                    progressCallback((processedBytes * 100) / totalBytes);
                }
            }
            lineStart = i + 1;
        }
    }
    
    exitProgrammingMode();
    return true;
}

bool PICProgrammer::enterProgrammingMode() {
    // Entry sequence for PIC18F46Q10
    setMCLR(true);
    delay(1);
    setMCLR(false);
    delayMicroseconds(100);
    
    // Send magic key
    sendBits(0x4D434850, 32); // MCHP
    
    // Verify entry
    sendBits(0x00, 8);
    uint16_t response = receiveBits(16);
    if(response != 0x1234) {
        setError("Failed to enter programming mode");
        return false;
    }
    
    return true;
}

void PICProgrammer::exitProgrammingMode() {
    setMCLR(true);
    delay(1);
}

bool PICProgrammer::bulkErase() {
    if(!executeCommand(CMD_BULK_ERASE)) {
        setError("Bulk erase command failed");
        return false;
    }
    
    // Wait for erase completion (P11 = 5ms typical)
    delay(10);
    return true;
}

bool PICProgrammer::loadConfiguration(uint32_t address, uint16_t data) {
    if(!executeCommand(CMD_LOAD_CONFIG)) {
        setError("Load configuration command failed");
        return false;
    }
    
    // Send address (24 bits)
    sendBits(address, 24);
    
    // Send data (16 bits)
    sendBits(data, 16);
    
    // Programming time for configuration memory (P10 = 5ms typical)
    delay(10);
    
    return true;
}

bool PICProgrammer::loadDataForProgMem(uint16_t data) {
    if(!executeCommand(CMD_LOAD_PROG_MEM)) {
        setError("Load program memory command failed");
        return false;
    }
    
    sendBits(data, 16);
    return true;
}

bool PICProgrammer::loadAddressForProgMem(uint32_t address) {
    currentAddress = address;
    
    if(!executeCommand(CMD_RESET_ADDRESS)) {
        setError("Reset address command failed");
        return false;
    }
    
    // Send address (24 bits)
    sendBits(address, 24);
    return true;
}

bool PICProgrammer::beginProgramming() {
    if(!executeCommand(CMD_BEGIN_PROG)) {
        setError("Begin programming command failed");
        return false;
    }
    
    // Programming time (P9 = 2.5ms typical)
    delay(3);
    return true;
}

bool PICProgrammer::incrementAddress() {
    if(!executeCommand(CMD_INCREMENT_ADD)) {
        setError("Increment address command failed");
        return false;
    }
    currentAddress++;
    return true;
}

bool PICProgrammer::verifyProgMem(uint32_t address, uint16_t expectedData) {
    if(!executeCommand(CMD_READ_PROG_MEM)) {
        setError("Read program memory command failed");
        return false;
    }
    
    uint16_t readData = receiveBits(16);
    if(readData != expectedData) {
        setError("Verification failed at address 0x" + String(address, HEX));
        return false;
    }
    
    return true;
}

void PICProgrammer::clockPulse() {
    setPGC(true);
    delayMicroseconds(1);  // Minimum clock high time (250ns)
    setPGC(false);
    delayMicroseconds(1);  // Minimum clock low time (250ns)
}

void PICProgrammer::sendBits(uint32_t data, uint8_t numBits) {
    for(int i = numBits-1; i >= 0; i--) {
        setPGD((data >> i) & 0x01);
        clockPulse();
    }
}

uint16_t PICProgrammer::receiveBits(uint8_t numBits) {
    uint16_t data = 0;
    pinMode(PIC_PGD, INPUT);
    
    for(int i = numBits-1; i >= 0; i--) {
        setPGC(true);
        delayMicroseconds(1);
        data |= (readPGD() << i);
        setPGC(false);
        delayMicroseconds(1);
    }
    
    pinMode(PIC_PGD, OUTPUT);
    return data;
}

bool PICProgrammer::executeCommand(uint8_t cmd) {
    sendBits(cmd, 8);
    return true;
}

void PICProgrammer::setError(const String& error) {
    lastError = error;
    Serial.println("PIC Programmer Error: " + error);
}

void PICProgrammer::setPGC(bool state) {
    digitalWrite(PIC_PGC, state);
}

void PICProgrammer::setPGD(bool state) {
    digitalWrite(PIC_PGD, state);
}

void PICProgrammer::setMCLR(bool state) {
    digitalWrite(PIC_MCLR, state);
}

bool PICProgrammer::readPGD() {
    return digitalRead(PIC_PGD);
}

bool PICProgrammer::parseHexLine(const char* line, uint32_t& address, uint8_t* data, uint8_t& length) {
    if(line[0] != ':') {
        setError("Invalid hex file format");
        return false;
    }
    
    // Parse record length
    char hex[3] = {line[1], line[2], 0};
    length = strtol(hex, NULL, 16);
    
    // Parse address
    char addr[5] = {line[3], line[4], line[5], line[6], 0};
    address = strtol(addr, NULL, 16);
    
    // Parse record type
    char type[3] = {line[7], line[8], 0};
    uint8_t recordType = strtol(type, NULL, 16);
    
    if(recordType != 0) {
        return false; // Skip non-data records
    }
    
    // Parse data
    for(int i = 0; i < length; i++) {
        char byte[3] = {line[9+i*2], line[10+i*2], 0};
        data[i] = strtol(byte, NULL, 16);
    }
    
    return true;
}
