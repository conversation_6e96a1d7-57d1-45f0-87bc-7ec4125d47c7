/*
    DIN Dimmer  
 5ch MOSFET Dimmer
 * Modbus map
 * Regs
 * from 0 to 10  = chan 1-10 brightness ( value 0 is off 1024 is full on)
 * from 11 to 20 = chan 1-10 fade in time in ms
 * from 21 to 30 = chan 1-10 fade out time in ms
 * from 31 to 40 = chan 1-10 min brightness (below min is full off)
 * from 41 to 50 = chan 1-10 max brightness (above max is full on)
 * from 51 to 54 = DS18B20 temp sensors value = temp * 100 (22.1C is 221)
 * 99 = board software version
 * Coils
 * from 0 to 10 = chan 1-10 lamp is ON ?
 */
#include "config.h"
#include "can.h"
#include <xc.h>
#include <string.h>
#include <stdbool.h>
#include <math.h>

// Define initial EEPROM values
/*
const unsigned char __at(0xF00000) eeprom_data[] = {
    //Brightness
    0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,
    //Fade in
    0,0,0,0,0,0,0,0,0,0,
    100,100,100,100,100,100,100,100,100,100,
    //Fade out
    0,0,0,0,0,0,0,0,0,0,
    100,100,100,100,100,100,100,100,100,100,
    //Min brightness
    0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,
    //Max brightness set to 1024
    0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,
    0,0,0,0,0,0,0,0,0,0,
};
 */


// Global variables for CAN message
volatile uint8_t rxData[8];
volatile uint16_t rxId;
volatile uint8_t rxLen;
volatile uint8_t messageReceived = 0;




#define BAUD_RATE 125 // speed in kbps
#define CPU_SPEED 64 // clock speed in MHz (4 clocks made up 1 instruction)




//Zero cross pin uses interrupt on change IOCAN.IOCAN2
#define ZeroCrossPin PORTAbits.RA2

#define TMR0_ON T0CON0bits.T0EN
#define TMR1_ON T1CONbits.TMR1ON
#define TMR2_ON T2CONbits.TMR2ON

//Offset in 10 us increment (value of 57 means 570us = 0.57ms)
#define ZCD_OFFSET 57

#define GAMMA 2.2
#define MAX_LEVEL 1000.0

#define CHAN_0 50
#define CHAN_1 50
#define CHAN_2 50
#define CHAN_3 50

volatile int gamma_value = 1;
volatile double chan_value = 1;
volatile int chan_dir = 1;

#define CHAN_0_MIN 200
#define CHAN_0_MAX 

#define CHAN_0_PIN LATCbits.LATC0
#define CHAN_1_PIN LATCbits.LATC1
#define CHAN_2_PIN LATCbits.LATC2
#define CHAN_3_PIN LATCbits.LATC3

volatile unsigned int PWM_Count = 0;
volatile int PWM_Offset = 0;
volatile int ZCD_Prediction_Count = 0;


void Config(void) {

    //Xtal config @ 64mhz
    // Set up the internal oscillator control register
    OSCCONbits.IRCF = 0b111;      // Set internal oscillator to 16 MHz
    OSCCONbits.SCS = 0b00;        // Select internal oscillator block
    
    // Enable PLL
    OSCTUNEbits.PLLEN = 1;        // Enable PLL, multiplying the clock by 4 to get 64 MHz

    // Disable the secondary oscillator
    //OSCCON2bits.SOSCGO = 0;       // Stop the secondary oscillator
    
    // Wait for the frequency to stabilize
    //while(!OSCCONbits.HFIOFS); // Wait for high-frequency internal oscillator to stabilize

    
    ConfigIO();

    //ConfigInterrupts();
    
    ConfigUnused();
    
    //ConfigTimer1();    

    ConfigCan();
            
    //// Timer2 used for ZCD prediction if a ZCD did not arrive on time:
    //// Prescaler=1:1; TMR2 PostScaler=1:1; PR2=80 - Freq = 100 000,00Hz - Period = 10 ms
    //T2CONbits.T2CKPS = 0b11;  // Prescaler is 1:16
    //T2CONbits.T2OUTPS = 0b1001; // Postscaler is 1:10
    //T2CONbits.TMR2ON = 0; // Timer2 on bit: 1=Timer2 is on;
    //PIE4bits.TMR2IE = 1;      // Enable Timer2 interrupt
    //PR2 = 9;             // Period register value for 10ms interval

    //Time to settle
    __delay_ms(100);
}

void ConfigIO(void) {
    
    ADCON0 = 0;

    TRISA = 0;
    
    TRISBbits.TRISB0 = 1; // ZCD input
    
    
    TRISBbits.TRISB2 = 0; // Set RB2 as output for CANTX
    TRISBbits.TRISB3 = 1; // Set RB3 as input for CANRX
    
    
    TRISCbits.TRISC0 = 0; // Chan 0
    TRISCbits.TRISC1 = 0; // Chan 1
    TRISCbits.TRISC2 = 0; // Chan 2
    TRISCbits.TRISC3 = 0; // Chan 3
}

void ConfigInterrupts(void) {
    //Enable interrupts
    INTCONbits.GIE = 1; //Global interrupts
    INTCONbits.PEIE = 1; //Peripherial interrupts
    INTCONbits.INT0IE = 1; //INT0 (RB0) Port Change Interrupt Enable 
    
    //Clear all interrupts
    PIE1 = 0;
    PIE2 = 0;
    PIE3 = 0;
    PIE4 = 0;
    PIE5 = 0;

    //Can receive
    PIE5bits.RXB0IE = 1;
    
    //There is no priority bit associated with INT0 (RB0); it is always
    //a high-priority interrupt source.
    INTCON2bits.INTEDG0 = 0; //RB0 Falling edge
        
    IPR1 = 0;
    IPR1bits.TMR1IP = 1; //Timer1 HIGH Priority
    IPR2 = 0;
    IPR3 = 0;
    IPR4 = 0;
    IPR5 = 0;
    IPR5bits.RXB0IP = 0; //CAN LOW Priority
    
}

void ConfigUnused(void) {
    /*
    //Disable DAC
    DAC1CON0bits.EN = 0;

    //FVR
    FVRCONbits.FVREN = 0;

    //Disable SPI / I2C
    SSP1CON1bits.SSPEN = 0;
     * */
}
/*
void ConfigTimer0(void) {
    // Set up Timer0 used for modbus
    // We need a fixed delay of 3 USART chars long
    // BAUD rate - Delay in ms
    // 9600         1.04
    // 19200        0.52
    // 38400        0.26
    // 57600        0.17
    // 115200       0.09

    T0CON0bits.T016BIT = 0; // 8-bit mode
    T0CON0bits.T0EN = 0; // Enable Timer0    
    T0CON1bits.T0CS = 0b010;  // Clock source: FOSC/4
    T0CON1bits.T0CKPS = 0b1000; // Prescaler: 1:256
    T0CON1bits.T0ASYNC = 1;
    TMR0H = 0; // Clear high byte of Timer0 (for 16-bit mode, if used)
    TMR0L = 68; // Preload value for 3ms
    
    PIE0bits.TMR0IE = 1; // Enable Timer0 interrupt
}
*/
void ConfigTimer1(void) {
    // Timer1 used to generate PWM for the mosfets
    // Internal clock (FOSC / 4)
    // Prescaler=1:1; TMR1 Preset=65456; Freq=100,00kHz; Period=10,00 µs
    // ISR Overhead cycles:18
    T1CONbits.T1CKPS = 0; // bits 5-4  Prescaler Rate Select bits
T1CONbits.TMR1ON = 0; // bit 0 enables timer
    T1CONbits.TMR1CS = 0b00; //clock source is the instruction clock (FOSC/4)
    T1CONbits.SOSCEN = 0; //SOSC is disabled for Timer1
    T1CONbits.nT1SYNC = 0;
    T1GCON = 0;
    PIE1bits.TMR1IE = 1; // Enable interrupts
    PIR1bits.TMR1IF = 0;
    
    TMR1H = 0xFF;     // preset for timer1 MSB register
    TMR1L = 0x72;     // preset for timer1 LSB register
}

void ConfigCan(void) {
    // Enter Configuration mode
    CANCON = 0x80;
    while ((CANSTAT & 0xE0) != 0x80);

    CIOCONbits.CLKSEL = 0; //Use the PLL as the source of the CAN system clock
    
    // Baud rate configuration for 125 kbps
    // Fosc = 64 MHz
    // Fcan = Fosc / 4 = 16 MHz
    // Desired Baud Rate = 125 kbps
    // BRP = 2 for 125 kbps (based on calculations)

    // BRGCON1: Baud Rate Control Register 1
    // SJW<1:0> Synchronization Jump Width bits
    // BRP<5:0> Baud Rate Prescaler bits
    BRGCON1 = 0x03; // BRP = 3 (4 TQ)

    // BRGCON2: Baud Rate Control Register 2
    // SEG2PHTS Phase Segment 2 Time Select bit
    // SAM Sample of the CAN Bus Line bit
    // SEG1PH<2:0> Phase Segment 1 bits
    // PRSEG<2:0> Propagation Time Segment bits
    BRGCON2 = 0xBA; // Propagation Segment = 7 TQ, Phase Segment 1 = 8 TQ, SAM = 0

    // BRGCON3: Baud Rate Control Register 3
    // WAKFIL Wake-up Filter bit
    // SEG2PH<2:0> Phase Segment 2 bits
    BRGCON3 = 0x05; // Phase Segment 2 = 8 TQ
    
    

    // Set the CAN module to Normal mode
    CANCON = 0x00;


    RXB0CON = 0x00; // Receive all valid messages

    // Set RXB0 to receive all messages (no filter)
    RXB0CONbits.RXM0 = 1;
    RXB0CONbits.RXM1 = 1;

    // Set CAN to Normal mode
    CANCON = 0x00;
    while ((CANSTAT & 0xE0) != 0x00);
    
}


void sendCanMessage() {
    messageStatus.timestamp = 0;// tQuarterSecSinceStart;
    
    CanHeader header;
    header.nodeID = 2;
    header.messageType = 1;
    
    CanMessage message;
    message.header = &header;
    
    // data length - equal to 6 for heartbeat and 1 for normal messages
    message.dataLength = 1;

    // data - set operation to be toggle, pass in the other args too to combine first byte all the time
    byte* data = &message.data;
    
    // the actual encoding/decoding has to match between relay and switch, wrapped inside can method below
    // use only transmit buffer errors, ignore receive buffer errors in data since we only care about sending CAN traffic over
    *data++ = 0xfe;//can_combineCanDataByte(operation, TXERRCNT, FIRMWARE_VERSION, switchCounter);
    //unsigned long timeSinceStart = tQuarterSecSinceStart / 4;
    
    /*
    if (messageType == HEARTBEAT) {
        // whole 2nd byte = CAN transmit error count read from the register
        *data++ = TXERRCNT;
        // whole 3rd byte = CAN receive error count read from the register
        *data++ = RXERRCNT;
        // another byte = firmware version
        *data++ = 1;
        // last 2 bytes = time since start. Ignore any values higher, only used in debugging anyway
        *data++ = (timeSinceStart >> 8) & MAX_8_BITS;
        *data++ = timeSinceStart & MAX_8_BITS;
    }
     * */
    // use the synchronous version to make sure the message is really sent before moving on
    can_sendSynchronous(&message);
}


void checkCanMessageReceived() {
    // check if CAN receive buffer 0 interrupt is enabled and interrupt flag set
    if (PIE5bits.RXB0IE && PIR5bits.RXB0IF) {
        // now confirm the buffer 0 is full
        if (RXB0CONbits.RXFUL) {
            if (RXB0DLCbits.DLC >= 1) { // make sure we received at least one byte in the CAN data frame
                // see setupCan above for more details, but we can either get NORMAL or COMPLEX messages in buffer 0, but we process them the same way actually
                //CanHeader header = can_idToHeader(&RXB0SIDH, &RXB0SIDL);
                // we need to know the nodeID (if it is equal to floor that the operation is for all lights)
                //receivedNodeID = header.nodeID;
                // and we need just 1 byte of data then
                //receivedDataByte = RXB0D0;
                // just set a flag since both of the above could actually be 0 (nodeID 0 could potentially be sent and databyte too)
                //receivedOperation = TRUE;
            }
            
            RXB0CONbits.RXFUL = 0; // mark the data in buffer as read and no longer needed
        }
        PIR5bits.RXB0IF = 0; // clear the interrupt flag now
    }
    
    // check if CAN receive buffer 1 interrupt is enabled and interrupt flag set
    if (PIE5bits.RXB1IE && PIR5bits.RXB1IF) {
        // now confirm the buffer 1 is full
        if (RXB1CONbits.RXFUL) {
            // see setupCan above for more details, but we can only get CONFIG or MAPPINGS messages in this buffer
            // which makes it a bit more robust since these shall be really lower priority unlike buffer 0 anyway
            // this time we do not need to know the nodeID since we know it is equal to floor as per setupCan where we use strict filter
            //CanHeader header = can_idToHeader(&RXB1SIDH, &RXB1SIDL);
            //if (CONFIG == header.messageType && RXB1DLCbits.DLC == 3) {
                // in this case we expect 3 bytes of data
                // first byte = number of the mapping - will drive address to store this at in EEPROM
                // second byte = nodeID of the mapping
                // last byte = output to set by this mapping (should be only up to 30 anyway)
                //receivedMappingNumber = RXB1D0;
                //receivedMappingNodeID = RXB1D1;
                //receivedMappingOutputNumber = RXB1D2;
            //} else if (MAPPINGS == header.messageType) { // in this case we do not care about data frame length
                //receivedMappingsRequest = TRUE;
            //}

            RXB1CONbits.RXFUL = 0; // mark the data in buffer as read and no longer needed
        }
        PIR5bits.RXB1IF = 0; // clear the interrupt flag now
    }
}


void can_send_message_old(uint16_t id, uint8_t *data, uint8_t len) {
    while(TXB0CONbits.TXREQ) {
        TXB0CONbits.TXREQ = 0;
        continue; // TX buffer is busy
    }

    // Set the standard identifier
    TXB0SIDH = (uint8_t)(id >> 3);
    TXB0SIDL = (uint8_t)(id << 5);

    // Load data into the transmit buffer
    for (uint8_t i = 0; i < len; i++) {
        *((&TXB0D0) + i) = data[i];
    }

    // Set data length
    TXB0DLC = len;

    // Request message transmission
    TXB0CONbits.TXREQ = 1;
}

void Initialize(void) {

    //Set default brightness
    for(char i = 0; i < 10; i++ ) {
        //modbusRegister[i] = 0;
    }
    
    //Set default fade in time
    for(char i = 10; i < 20; i++ ) {
        //modbusRegister[i] = 100;
    }
    
    //Set default fade out time
    for(char i = 20; i < 30; i++ ) {
        //modbusRegister[i] = 100;
    }
    
    //Set default Min brightness
    for(char i = 30; i < 40; i++ ) {
        //modbusRegister[i] = 30;
    }
    
    //Set default Mаx brightness
    for(char i = 40; i < 50; i++ ) {
        //modbusRegister[i] = 1024;
    }
     
}

void main(void) {

    Config();
    Initialize();
    
    
    //can_init();
    
    // first move to CONFIG mode
    //can_setMode(CONFIG_MODE);

    //can_setupBaudRate(BAUD_RATE, CPU_SPEED);
    
    // now setup CAN to receive only CONFIG message types for this node ID
    //CanHeader header;
    //header.nodeID = nodeID;
    //header.messageType = CONFIG;
    //can_setupStrictReceiveFilter(&header);
    
    // Set RXB0 to receive all messages (no filter)
    //RXB0CONbits.RXM0 = 1;
    //RXB0CONbits.RXM1 = 1;
    
    // switch CAN to normal mode (using real underlying CAN)
    //can_setMode(NORMAL_MODE);
    
    // now enable CAN interrupts (only in debug mode, otherwise not needed and not used, just unnecessary load)
    //if (DEBUG) {
        PIE5bits.ERRIE = 1;  // Enable CAN BUS error interrupt
        //
    //PIE5bits.TXB0IE = 1; // Enable Transmit Buffer 0 Interrupt        
    //}
    
    //Can receive
    PIE5bits.RXB0IE = 1;
    IPR5bits.RXB0IP = 1; //CAN LOW Priority
    
    unsigned char cnt = 0;
    while (true) {
        __delay_ms(500);
        
        // Example CAN message data
        uint8_t txData[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};

        
        LATAbits.LATA1 = !LATAbits.LATA1;
     
        if (messageReceived) {
            // Process received message (toggle an LED, etc.)
            
            
            txData[7] = cnt ++;
            
            // Send a CAN message to master
            //can_send_message_old(0, txData, 8);
            //sendCanMessage();
            
            // Clear the message received flag
            messageReceived = 0;
        }

   continue;
    

        //chan_value = 978; is max
        //chan_value = 978 / 2;
        //__delay_ms(10);

        if (chan_dir == 1) {
            chan_value++;
            if (chan_value > 1000) {
                chan_dir = 0;
            }
        } else {
            chan_value--;
            if (chan_value <= 0) {
                chan_dir = 1;
            }
        }

        gamma_value = (uint8_t) (pow( (chan_value / MAX_LEVEL), GAMMA) * MAX_LEVEL + 0.5);

        //    PORTA = 0xff;
        __delay_ms(2000);
        //  PORTA = 0;
        //__delay_ms(1);
    }

    return;
}


void timer1Reset() {
    TMR1H = 0xFF;     // preset for timer1 MSB register
    TMR1L = 0x72;     // preset for timer1 LSB register
    
    PIR1bits.TMR1IF = 0;
}


void ZeroCrossDetected(void) {
    //Stop ZCD prediction timer
    TMR2_ON = 0;

    PWM_Offset = ZCD_OFFSET;
    timer1Reset();
}


// High-priority ISR
void __interrupt(high_priority) ISR_HIGH(void) {

    
    //On pin change
    if (INTCONbits.INT0IF) {
        //Zero cross detected
        ZeroCrossDetected();
        INTCONbits.INT0IF = 0;
    }
    
    checkCanMessageReceived();
    
    //Timer1 overflow
    if (PIR1bits.TMR1IF) {
        //First clear the flag, because the commands bellow add up to the timer delay
        timer1Reset();

        PWM_Count++;

        if (PWM_Offset > 0) {
            PWM_Offset--;
            //The real zero cross
            if (PWM_Offset == 0) {
                //Start PWM cycle
                PWM_Count = 0;
                //Start Timer2 to predict the next ZeroCross
                //ZCD_Prediction_Count = 0;
                //TMR2_ON = 0;
                //TMR2 = 0;
            }
        }

        //CHAN_1_PIN = !CHAN_1_PIN;
        

        CHAN_0_PIN = chan_value > PWM_Count;
        CHAN_1_PIN = gamma_value > PWM_Count;
        CHAN_2_PIN = chan_value > PWM_Count;
        CHAN_3_PIN = chan_value > PWM_Count;


        if (PWM_Count >= 979) {
            //ZCD not found
            //PWM_Count = 0;
        }
    }
    
    
    
    /*
    //Timer2 overflow
    if (PIR4bits.TMR2IF) {
        //Clear the flag
        // Period register value for 10ms interval
        PR2 = 9;
        PIR4bits.TMR2IF = 0;

        //ZCD_Prediction_Count++;

        //CHAN_1_PIN = !CHAN_1_PIN;

        //if(ZCD_Prediction_Count == 1000) {

        //ZCD is missing, continue with timer2
        //Reset PWM 
        //PWM_Offset = 0;
        //PWM_Count = 0;
        //timer1Reset();

        //CHAN_1_PIN = !CHAN_1_PIN;

        //Zero cross should occur right now
        //ZCD_Prediction_Count = 0;
        //}
    }
    */
}

// Low-priority ISR
void __interrupt(low_priority) ISR_LOW(void) {

    
    checkCanMessageReceived();
    return;
    
    if (PIR5bits.RXB0IF) {
        // CAN receive buffer 0 interrupt
        // Read the identifier
        rxId = ((uint16_t)RXB0SIDH << 3) | (RXB0SIDL >> 5);

        // Read the data length
        rxLen = RXB0DLC & 0x0F;

        // Read the data
        for (uint8_t i = 0; i < rxLen; i++) {
            rxData[i] = *((&RXB0D0) + i);
        }

        // Clear the receive flag
        RXB0CONbits.RXFUL = 0;
        PIR5bits.RXB0IF = 0;

        // Set the flag to indicate message received
        messageReceived = 1;
    }    
}