// EventManager.cpp

#include "EventManager.h"

// Initialize the currentIndex to 0
EventManager::EventManager() : currentIndex(0) {}

// Function to get the singleton instance
EventManager& EventManager::getInstance() {
    static EventManager instance;
    return instance;
}

void EventManager::begin() {
    logEvent(EventType::INIT);
}

// Log an event
void EventManager::logEvent(EventType type, uint8_t param1, uint8_t param2) {

    if(!DeviceSettings::getInstance().getEventLogEnabled()) {
        return;
    }

    shiftEvents();
    
    Event& event = events[currentIndex];
    event.type = type;
    event.timestamp = (millis() / 1000);
    event.param_1 = param1;
    event.param_2 = param2;
    currentIndex++;
}


void EventManager::logEvent(EventType type, String info, uint8_t param1, uint8_t param2) {

    if(!DeviceSettings::getInstance().getEventLogEnabled()) {
        return;
    }

    shiftEvents();
    
    Event& event = events[currentIndex];
    event.type = type;
    event.timestamp = (millis() / 1000);
    event.param_1 = param1;
    event.param_2 = param2;
    event.info = info;
    currentIndex++;
}


void EventManager::shiftEvents() {
    // Check if the queue is full
    if (currentIndex >= MAX_EVENTS) {
        // Shift all elements to the left by one
        for (int i = 1; i < MAX_EVENTS; i++) {
            events[i - 1] = events[i];
        }
        // Adjust the index to the last element
        currentIndex = MAX_EVENTS - 1;
    }
}


// Private method to print an individual event
void EventManager::printEvents(EthernetClient client) {
    
    for(int i = 0; i < currentIndex; i++) {
        
        printTimestamp(client, events[i].timestamp);
        //client.print(": ");

        switch (events[i].type)
        {
            case EventType::INIT:
                client.print(" - Initialize");
                break;
            case EventType::INPUT_CHANGE:
                client.print(" - Input[");
                client.print(events[i].param_1);
                client.print("] set state to ");
                client.print(events[i].param_2);
                break;
            case EventType::WEB:
                client.print(" - Web Click on Switch[");
                client.print(events[i].param_1);
                client.print("] set state to ");
                client.print(events[i].param_2);
                break;
            case EventType::MQTT_BRIGHTNESS:
                client.print(" - MQTT channel [");
                client.print(events[i].param_1);
                client.print("] set brightness to ");
                client.print(events[i].param_2);
                break;
            case EventType::MQTT_STATE:
                client.print(" - MQTT channel [");
                client.print(events[i].param_1);
                client.print("] set state to ");
                client.print(events[i].param_2);
                break;
            //case EventType::CANBUS:
                //break;
            case EventType::OVERHEAT:
                client.print(" - OVERHEAT at ");
                client.print(events[i].param_1);
                client.print("C");
                break;
            case EventType::ERROR:
                client.print(" - ");
                client.print(events[i].info);
                break;
            default:
                client.print(" - Unhandled event");
                break;
        }
        client.println();
    }
}


void EventManager::printTimestamp(EthernetClient client, int32_t timestamp) {
     int seconds = 0;
    int minutes = 0;
    int hours = 0;
    int days = 0;
    //int32_t esp32_seconds_since_start = timestamp / 1000000;

    if(timestamp > 86400) {
        days = timestamp / 86400;
        timestamp = timestamp % 86400;
    }

    if(timestamp > 3600) {
        hours = timestamp / 3600;
        timestamp = timestamp % 3600;
    }

    if(timestamp > 60) {
        minutes = timestamp / 60;
        timestamp = timestamp % 60;
    }

    seconds = timestamp;
    
    client.print(days);
    client.print("d ");
    client.print(hours);
    client.print("h ");
    client.print(minutes);
    client.print("m ");
    client.print(seconds);
    client.print("s");
    
}