#ifndef PIC_PROGRAMMER_H
#define PIC_PROGRAMMER_H

#include <Arduino.h>
#include "global_config.h"

// PIC18F46Q10 Programming Commands
#define CMD_LOAD_CONFIG    0x00
#define CMD_LOAD_PROG_MEM 0x02
#define CMD_READ_PROG_MEM 0x04
#define CMD_INCREMENT_ADD 0x06
#define CMD_RESET_ADDRESS 0x16
#define CMD_BEGIN_PROG    0x08
#define CMD_BULK_ERASE    0x18
#define CMD_ROW_ERASE     0xF0

// Memory regions
#define CONFIG_START    0x300000
#define CONFIG_END     0x30000D
#define PROGRAM_START  0x000000
#define PROGRAM_END    0x007FFF

class PICProgrammer {
public:
    static PICProgrammer& getInstance();
    
    void begin();
    bool programHexFile(const uint8_t* hexData, size_t length, void (*progressCallback)(int) = nullptr);
    String getLastError() const { return lastError; }
    
private:
    PICProgrammer();
    
    // Programming sequence methods
    bool enterProgrammingMode();
    void exitProgrammingMode();
    bool bulkErase();
    bool loadConfiguration(uint32_t address, uint16_t data);
    bool loadDataForProgMem(uint16_t data);
    bool loadAddressForProgMem(uint32_t address);
    bool beginProgramming();
    bool incrementAddress();
    bool verifyProgMem(uint32_t address, uint16_t expectedData);
    
    // Low-level communication
    void setPGC(bool state);
    void setPGD(bool state);
    void setMCLR(bool state);
    bool readPGD();
    void clockPulse();
    void sendBits(uint32_t data, uint8_t numBits);
    uint16_t receiveBits(uint8_t numBits);
    
    // Utility functions
    bool parseHexLine(const char* line, uint32_t& address, uint8_t* data, uint8_t& length);
    void setError(const String& error);
    bool executeCommand(uint8_t cmd);
    
    String lastError;
    void (*progressCallback)(int);
    uint32_t currentAddress;
};

#endif
