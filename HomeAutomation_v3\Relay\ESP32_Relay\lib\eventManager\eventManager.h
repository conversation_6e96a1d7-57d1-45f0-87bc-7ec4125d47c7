// EventManager.h

#ifndef EVENT_MANAGER_H
#define EVENT_MANAGER_H

#include <Arduino.h>
#include "deviceSettings.h"
#include "Ethernet.h"

class EventManager {
public:
    // Enum for event types
    enum EventType {
        INIT,
        OUTPUT_CHANGE,
        INPUT_CHANGE,
        WEB,
        MQTT_BRIGHTNESS,
        MQTT_STATE,
        CANBUS,
        OVERHEAT,

        ERROR
        // Add more event types as needed
    };

    // Function to get the singleton instance
    static EventManager& getInstance();

    void begin();

    // Log an event
    void logEvent(EventType type, uint8_t param1 = 0, uint8_t param2 = 0);
    void logEvent(EventType type, String info, uint8_t param1 = 0, uint8_t param2 = 0);

    // Dump all events 
    void printEvents(EthernetClient client);

private:
    // Private constructor for singleton
    EventManager();

    // Maximum number of events to store
    static const int MAX_EVENTS = 50;

    // Struct to represent an event
    struct Event {
        EventType type;
        uint32_t timestamp;
        uint8_t param_1;
        uint8_t param_2;
        String info;
        bool state;
    };

    // Array to store events
    Event events[MAX_EVENTS];

    // Index to keep track of the next available slot in the array
    int currentIndex;   

    void shiftEvents();
    void printTimestamp(EthernetClient client, int32_t timestamp);
};

#endif  // EVENT_MANAGER_H
