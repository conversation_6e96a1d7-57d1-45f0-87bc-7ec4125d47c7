#ifndef CONFIG_H
#define CONFIG_H

#define DEBUG_TO_UART 1

#define APP_SOFTWARE_VERSION 1

#define NUM_INPUTS       12
#define NUM_OUTPUTS     5


#define MAX_TEMPERATURE 70

#define DIMMER_MAX_STEP 500
#define DIMMER_BUTTON_STEP 25

#define LAN_RESET 13
#define LAN_INTERRUPT 5

//LAN_CS is used in the Ethernet lib by the name PIN_SPI_SS_ETHERNET_LIB
#define PIN_SPI_SS_ETHERNET_LIB 4

#define CAN_RX_PIN GPIO_NUM_21
#define CAN_TX_PIN GPIO_NUM_22

#define PIC_RX_PIN 16
#define PIC_TX_PIN 17
#define PIC_INTERRUPT_PIN 25

#define STATUS_LED 15

#define ONE_WIRE_BUS 33


#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)



#endif