#include <Arduino.h>
#include "global_config.h"
#include <WiFi.h>
#include <Wire.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <ArduinoHA.h>
#include "eventManager.h"
#include "deviceSettings.h"
#include "WebServer.h"
#include "ioModule.h"
#include "statusLed.h"
#include <ld2410.h>


//#include <cc1101.h>
//#include <3outof6.h>
//#include <mbus_packet.h>

//const char *kitchen_cold_id = "00443301";
//const char *kitchen_warm_id = "01285363";
//const char *bath_cold_id = "00443337";
//const char *bath_warm_id = "01294154";

// RX - Buffers
//uint8_t RXpacket[291];
//uint8_t RXbytes[584];

//RXinfoDescr RXinfo;

//bool is_rf_receiving = false;

EthernetClient mqqtClient;

WebServer webServer(80);

HADevice HA_device;
HAMqtt mqtt(mqqtClient, HA_device, 32);

char *mqtt_username;
char *mqtt_password;

HABinarySensor haSensorInput1("Input1");
HABinarySensor haSensorInput2("Input2");
HABinarySensor haSensorInput3("Input3");
HABinarySensor haSensorInput4("Input4");
HABinarySensor haSensorInput5("Input5");
HABinarySensor haSensorInput6("Input6");
HABinarySensor haSensorInput7("Input7");
HABinarySensor haSensorInput8("Input8");

HALight haLightOutput1("Output1");
HALight haLightOutput2("Output2");
HALight haLightOutput3("Output3");
HALight haLightOutput4("Output4");
HALight haLightOutput5("Output5");
HALight haLightOutput6("Output6");
HALight haLightOutput7("Relay1");
HALight haLightOutput8("Relay2");
//HALight haLightOutput9("RF Enable");

//HASensorNumber watermeter_kitchen_cold("watermeter_kitchen_cold", HASensorNumber::PrecisionP1);
//HASensorNumber watermeter_kitchen_warm("watermeter_kitchen_warm", HASensorNumber::PrecisionP1);
//HASensorNumber watermeter_bath_cold("watermeter_bath_cold", HASensorNumber::PrecisionP1);
//HASensorNumber watermeter_bath_warm("watermeter_bath_warm", HASensorNumber::PrecisionP1);

HASensorNumber kitchenTemp("KitchenTemp", HASensorNumber::PrecisionP2);
HASensorNumber livingroomTemp("LivingroomTemp", HASensorNumber::PrecisionP2);
HASensorNumber kitchenHum("KitchenHum", HASensorNumber::PrecisionP2);
HASensorNumber livingroomHum("LivingroomHum", HASensorNumber::PrecisionP2);

//Ir
HASensor kitchenInfrared("KitchenIR", HASensorNumber::PrecisionP2);
HASensor livingroomInfrared("LivingroomIR", HASensorNumber::PrecisionP2);

HASensorNumber haTemperatureSensor("Temperature", HASensorNumber::PrecisionP2);

HABinarySensor* haBynaryDevices[] = {&haSensorInput1, &haSensorInput2, &haSensorInput3, &haSensorInput4, &haSensorInput5, &haSensorInput6, &haSensorInput7, &haSensorInput8};
HALight*        haLightDevices[] = {&haLightOutput1, &haLightOutput2, &haLightOutput3, &haLightOutput4, &haLightOutput5, &haLightOutput6, &haLightOutput7, &haLightOutput8};

const int haBynaryDevicesSize = 8;//sizeof(haBynaryDevices) / sizeof(haBynaryDevices[0]);
const int haLightDevicesSize = 8;//sizeof(haLightDevices) / sizeof(haLightDevices[0]);

OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature tempSensor(&oneWire);

const int DHTtemperatureUpdateTimeout = 60000; //in ms
const int temperatureUpdateTimeout = 30000; //in ms
const int temperatureUpdateReadyTimeout =  temperatureUpdateTimeout + 750; //in ms
unsigned long previousTemperatureUpdate = 0;
unsigned long previousDHTTemperatureUpdate = 0;
unsigned long DHTpreviousTemperatureUpdate = 0;
bool isTempConversionInProgress = false;

//DHT sensor
bool DHT_VALID;
float DHT_HUM, DHT_TEMP,  dht_kitchen_temp, dht_livingroom_temp, dht_kitchen_hum, dht_livingroom_hum;

//IR Sensor
unsigned long IRData;
unsigned long previousIRKey = 0;
const int IRKeyTimeout = 600; //in ms


ld2410 radar;

uint32_t lastReading = 0;
bool radarConnected = false;



/*
IRAM_ATTR void rxFifoISR()
{
  uint8_t fixedLength;
  uint8_t bytesDecoded[2]; 
    
  Serial.println("rxFifoISR");

  // - RX FIFO 4 bytes detected - 
  // Calculate the total length of the packet, and set fixed mode if less
  // than 255 bytes to receive
  if (RXinfo.start == true)
  {  
    // Read the 3 first bytes
    cc1101_readBurstReg(RXinfo.pByteIndex, CC1101_RXFIFO, 3);
    
    // - Calculate the total number of bytes to receive -
    // Possible improvment: Check the return value from the deocding function,
    // and abort RX if coding error. 
    decode3outof6(RXinfo.pByteIndex, bytesDecoded, 0);
    RXinfo.lengthField = bytesDecoded[0];
    RXinfo.length = byteSize((packetSize(RXinfo.lengthField)));
    
    
    // - Length mode - 
    // Set fixed packet length mode is less than 256 bytes
    if (RXinfo.length < (MAX_FIXED_LENGTH))
    {
      cc1101_writeReg(CC1101_PKTLEN, (uint8_t)(RXinfo.length));
      cc1101_writeReg(CC1101_PKTCTRL0, FIXED_PACKET_LENGTH);
      RXinfo.format = FIXED;
    }
    
    // Infinite packet length mode is more than 255 bytes
    // Calculate the PKTLEN value
    else
    {
      fixedLength = RXinfo.length  % (MAX_FIXED_LENGTH);
      cc1101_writeReg(CC1101_PKTLEN, (uint8_t)(fixedLength));
    } 
                
    RXinfo.pByteIndex += 3;
    RXinfo.bytesLeft   = RXinfo.length - 3;
    
    // Set RX FIFO threshold to 32 bytes
    RXinfo.start = false;
    cc1101_writeReg(CC1101_FIFOTHR, RX_FIFO_THRESHOLD);
  }
  
  // - RX FIFO Half Full detected - 
  // Read out the RX FIFO and set fixed mode if less
  // than 255 bytes to receive
  else
  {
    // - Length mode -
    // Set fixed packet length mode is less than 256 bytes
    if (((RXinfo.bytesLeft) < (MAX_FIXED_LENGTH )) && (RXinfo.format == INFINITE))
    {
      cc1101_writeReg(CC1101_PKTCTRL0, FIXED_PACKET_LENGTH);
      RXinfo.format = FIXED;
    }

    // Read out the RX FIFO
    // Do not empty the FIFO (See the CC110x or 2500 Errata Note)
    cc1101_readBurstReg(RXinfo.pByteIndex, CC1101_RXFIFO, RX_AVAILABLE_FIFO - 1);

    RXinfo.bytesLeft  -= (RX_AVAILABLE_FIFO - 1);
    RXinfo.pByteIndex += (RX_AVAILABLE_FIFO - 1);
  }
}

IRAM_ATTR void rxPacketRecvdISR(void)
{
  // This function is called when the complete packet has been received. 
  // The remaining bytes in the RX FIFO are read out, and packet complete signalized
  Serial.println("rxPacketRecvdISR");
  // Read remaining bytes in RX FIFO
  cc1101_readBurstReg(RXinfo.pByteIndex, CC1101_RXFIFO, (uint8_t)RXinfo.bytesLeft);
  RXinfo.complete = true;
}

uint16_t startReceiving(uint8_t* packet, uint8_t* bytes)
{ 
  uint16_t rxStatus;
    
  // Initialize RX info variable
  RXinfo.lengthField = 0;           // Length Field in the wireless MBUS packet
  RXinfo.length      = 0;           // Total length of bytes to receive packet
  RXinfo.bytesLeft   = 0;           // Bytes left to to be read from the RX FIFO
  RXinfo.pByteIndex  = bytes;       // Pointer to current position in the byte array
  RXinfo.format      = INFINITE;    // Infinite or fixed packet mode
  RXinfo.start       = true;        // Sync or End of Packet
  RXinfo.complete    = false;       // Packet Received

 
  // Set RX FIFO threshold to 4 bytes
  cc1101_writeReg(CC1101_FIFOTHR, RX_FIFO_START_THRESHOLD);

  // Set infinite length 
  cc1101_writeReg(CC1101_PKTCTRL0, INFINITE_PACKET_LENGTH);

  // Check RX Status
  rxStatus = cc1101_readReg(CC1101_SNOP, READ_SINGLE);
  if ( (rxStatus & 0x70) != 0)
  {
    // Abort if not in IDLE
    cc1101_cmdStrobe(CC1101_SIDLE); // Enter IDLE state
    return (RX_STATE_ERROR);
  }
  
  // Flush RX FIFO
  // Ensure that FIFO is empty before reception is started
  cc1101_cmdStrobe(CC1101_SFRX); // flush receive queue

  attachInterrupt(digitalPinToInterrupt(CC1101_GDO0), rxFifoISR, RISING);
  attachInterrupt(digitalPinToInterrupt(CC1101_GDO2), rxPacketRecvdISR, FALLING);
  
  // Strobe RX
  cc1101_cmdStrobe(CC1101_SRX); // Enter RX state

  // Wait for FIFO being filled
  //while (RXinfo.complete != true)
  //{
    //delay(1);
  //}
  
  return (PACKET_OK);
    
}

uint16_t stopReceiving(uint8_t* packet, uint8_t* bytes) {
  
  uint16_t rxStatus;

  detachInterrupt(digitalPinToInterrupt(CC1101_GDO0));
  detachInterrupt(digitalPinToInterrupt(CC1101_GDO2));

  // Check that transceiver is in IDLE
  rxStatus = cc1101_readReg(CC1101_SNOP, READ_SINGLE);
  if ( (rxStatus & 0x70) != 0) {
    cc1101_cmdStrobe(CC1101_SIDLE); // Enter IDLE state
    return (RX_STATE_ERROR);
  }

  rxStatus = decodeRXBytesTmode(bytes, packet, packetSize(RXinfo.lengthField));
  
  return (rxStatus);
}

// Decode mkradio4 telegrams
void decodeTechemWaterMeters() {
  //The right packet size
  if(RXpacket[0] == 0x2f) {
    //manufacturer TCH (0x6850)
    if(RXpacket[2] == 0x68 && RXpacket[3] == 0x50) {
      Serial.println();
      Serial.print("ID: ");

      char device_id[10];
      sprintf(device_id, "%02x%02x%02x%02x", RXpacket[7], RXpacket[6], RXpacket[5], RXpacket[4]);
      Serial.print(device_id);

      //Serial.print(" Type: ");
      if(RXpacket[9] == 0x72) {
        //Serial.print("Cold");
      } else if(RXpacket[9] == 0x62) {
        //Serial.print("Warm");
      } else {
        //Serial.print("Unknown");
      }

       uint8_t prev_lo = RXpacket[16];
       uint8_t prev_hi = RXpacket[17];
       uint32_t prev = (256 * prev_hi + prev_lo);
       //float prev = (256.0 * prev_hi + prev_lo) / 10.0;

       uint8_t curr_lo = RXpacket[20];
       uint8_t curr_hi = RXpacket[21];
       uint32_t current = (256 * curr_hi + curr_lo);
       //float curr = (256.0 * curr_hi + curr_lo) / 10.0;

       //float total_water_consumption_m3 = prev + curr;
       //float target_water_consumption_m3 = prev;

       //Serial.print("Calc Total: ");
       //Serial.print(total_water_consumption_m3);
       //Serial.print(" Target: ");
       //Serial.println(target_water_consumption_m3);

       float total = (current + prev) / 10.0;
       //Serial.println(total);
       //Serial.print("Send to mqtt: "); 
      if(strcmp(device_id, bath_cold_id) == 0) { //RXpacket[7] == 0x00 && RXpacket[6] == 0x44 && RXpacket[5]== 0x33 && RXpacket[4] == 0x37) {
        //Bath Cold - 00443337
        watermeter_bath_cold.setValue(total, true);
      } else if(strcmp(device_id, bath_warm_id) == 0) { //RXpacket[7] == 0x01 && RXpacket[6] == 0x29 && RXpacket[5]== 0x41 && RXpacket[4] == 0x54) {
        //Bath Warm - 01294154
        watermeter_bath_warm.setValue(total, true);
      } else if(strcmp(device_id, kitchen_cold_id) == 0) { //RXpacket[7] == 0x00 && RXpacket[6] == 0x44 && RXpacket[5]== 0x33 && RXpacket[4] == 0x01) {
        //Kitchen Cold - 00443301
        watermeter_kitchen_cold.setValue(total, true);
      } else if(strcmp(device_id, kitchen_warm_id) == 0) { //RXpacket[7] == 0x01 && RXpacket[6] == 0x28 && RXpacket[5]== 0x53 && RXpacket[4] == 0x63) {
        //Kitchen Warm - 01285363
        watermeter_kitchen_warm.setValue(total, true);
      }
    }
  }
}
*/



void detectsIRData(int IRSensor) {

  unsigned long previousTime = micros();
  unsigned long passedTime = 0;
  unsigned long timeOut = 10500;

  //wait the first low pulse - 4.4ms for samsung, 9ms for nec
  previousTime = micros();    
  while (digitalRead(IRSensor) == LOW) {
    delayMicroseconds(10);
    if((unsigned long)(micros() - previousTime) >= timeOut) {
      return; //timeout
    }
  }
  
  passedTime = micros() - previousTime;
  if (passedTime < 2000 ) { 
    //Noice
    return;
  }

  timeOut = 5500;
  previousTime = micros();      
  // Start counting and make sure we are not here for more than 5500 ms
  while (digitalRead(IRSensor) == HIGH) {
    delayMicroseconds(10);
    if((unsigned long)(micros() - previousTime) >= timeOut) {
      return; //timeout
    }
  }

  passedTime = micros() - previousTime;

  //The first pulse is ~4.477 ms
   if (passedTime >= 4200 && passedTime <= 5000) {      

      unsigned long IRData =0;
      //unsigned long dat[32];
      for(int IRCounter = 0; IRCounter < 32; IRCounter++) {

          previousTime = micros();
          timeOut = 5000;
          
          while (digitalRead(IRSensor) == LOW) {
            delayMicroseconds(10);
            if((unsigned long)(micros() - previousTime) >= timeOut) {
              break;
            }        
          }
                      
          while (digitalRead(IRSensor) == HIGH) {
            delayMicroseconds(10);
            if((unsigned long)(micros() - previousTime) >= timeOut) {
              break;
            }        
          }

          passedTime = micros() - previousTime;
          //dat[IRCounter] = passedTime;
          
          //Nec
          //Logical '0' – a 562.5µs pulse burst followed by a 562.5µs space, with a total transmit time of 1.125ms
          //Logical '1' – a 562.5µs pulse burst followed by a 1.6875ms space, with a total transmit time of 2.25ms

          //Samsung
          //Logical '1' – a 590μs pulse burst followed by a 1.6875ms space, with a total transmit time of 2.27ms
          //Logical '0' – a 590μs pulse burst followed by a 590μs space, with a total transmit time of 1.18ms


          if (passedTime > 2000 && passedTime < 2500) {      //~2.25ms pulse width is 1 , ~1.12ms pulse width is 0
            IRData |= (1<<(31-IRCounter)); //SetBit
          }

          if(passedTime > 2500) {
            return; //timeout
          }
      }
      
      char mystr[32];
      sprintf(mystr,"%lu",IRData);

      if(IRSensor == IR_KITCHEN) {
        kitchenInfrared.setValue(mystr);
      } else {
        livingroomInfrared.setValue(mystr);
      }

      #ifdef DEBUG_TO_UART
        Serial.println("IRData: ");
        Serial.println(IRData, HEX);
      #endif
  }

  previousIRKey = millis();
}

unsigned char ReadDHT(int DHT_PIN) {
  unsigned char DHT_read = 0;
  unsigned char cnt;
  for (cnt = 0; cnt < 8; cnt++) {
      while (digitalRead(DHT_PIN) == LOW);

      delayMicroseconds(40);

      if (digitalRead(DHT_PIN) == HIGH) {
          DHT_read |= (1 << (7 - cnt));
      }

      while (digitalRead(DHT_PIN) == HIGH);
  }
  return DHT_read;
}

void ReadDHTData(int DHT_PIN) {
  unsigned char T_Byte1;
  unsigned char T_Byte2;
  unsigned char RH_Byte1;
  unsigned char RH_Byte2;

  unsigned char DHT_Checksum;

  T_Byte1 = T_Byte2 = RH_Byte1 = RH_Byte2 = 0;
  DHT_VALID = true;

  // pull pin low for 2 milliseconds 
  pinMode(DHT_PIN, OUTPUT);
  digitalWrite(DHT_PIN, LOW);
  
  delay(2);

  digitalWrite(DHT_PIN, HIGH);
  // prepare to read the pin 
  pinMode(DHT_PIN, INPUT);

  delayMicroseconds(25);
  
  if (digitalRead(DHT_PIN) == LOW) {
      delayMicroseconds(90);
      if (digitalRead(DHT_PIN) == HIGH) {
          //Sensor is here
          while (digitalRead(DHT_PIN) == HIGH);

          //Here is the data
          RH_Byte1 = ReadDHT(DHT_PIN);
          RH_Byte2 = ReadDHT(DHT_PIN);
          T_Byte1 = ReadDHT(DHT_PIN);
          T_Byte2 = ReadDHT(DHT_PIN);
          DHT_Checksum = ReadDHT(DHT_PIN);

          if (DHT_Checksum != ((RH_Byte1 + RH_Byte2 + T_Byte1 + T_Byte2) & 255)) {  //Checksum error
              DHT_VALID = false; //Serial.println("DHT22 checksum error\r\n");
          } else {
              DHT_HUM = (float)((RH_Byte1 << 8) + RH_Byte2) / 10;
              DHT_TEMP = (float)(((T_Byte1 & 0x7F) << 8) + T_Byte2) / 10;
    
              if (T_Byte1 & 0x80) {
                  DHT_TEMP = -DHT_TEMP;
              }
          }
      } else {
          DHT_VALID = false;  Serial.println("Sensor error\n");
      }
  } else {
      DHT_VALID = false;  Serial.println("Sensor missing\n");
  }
}



void onMqttStateChanged(HAMqtt::ConnectionState state) {
    int8_t int_state = static_cast<int8_t>(state);

    switch (int_state)
    {
      case -5:
        Serial.println(F("MQTT state changed to: StateConnecting"));
        break;
      case -4:
        Serial.println(F("MQTT state changed to: StateConnectionTimeout"));
        break;
      case -3:
        Serial.println(F("MQTT state changed to: StateConnectionLost"));
        break;
      case -2:
        Serial.println(F("MQTT state changed to: StateConnectionFailed"));
        break;
      case -1:
        Serial.println(F("MQTT state changed to: StateDisconnected"));
        break;
      case 0:
        Serial.println(F("MQTT state changed to: StateConnected"));
        break;
      case 1:
        Serial.println(F("MQTT state changed to: StateBadProtocol"));
        break;
      case 2:
        Serial.println(F("MQTT state changed to: StateBadClientId"));
        break;
      case 3:
        Serial.println(F("MQTT state changed to: StateUnavailable"));
        break;
      case 4:
        Serial.println(F("MQTT state changed to: StateBadCredentials"));
        break;
      case 5:
        Serial.println(F("MQTT state changed to: StateUnauthorized"));
        break;
      default:
        break;
    }

    Serial.println(static_cast<int8_t>(state));
}

void onStateCommand(bool state, HALight* sender) {
  #ifdef DEBUG_TO_UART
    Serial.print("State: ");
    Serial.println(state);
  #endif
    for(int i = 0; i < haLightDevicesSize; i++) {
      if(haLightDevices[i] == sender) {
        IOModule::getInstance().setOutput(i, (state == true ? 1 : 0));
        
        if(DeviceSettings::getInstance().getEventLogEnabled()) {
          EventManager::getInstance().logEvent(EventManager::EventType::MQTT_STATE, i, (state == true)? 1 : 0);
        }
        break;
      }
    }

    sender->setState(state); // report state back to the Home Assistant
}


void initHADevices() {

#ifdef DEBUG_TO_UART
  Serial.println("INIT - HA Devices");
#endif

  const byte *deviceId = DeviceSettings::getInstance().getMQTTDeviceID();

  HA_device.setUniqueId(deviceId, 6);
  HA_device.setSoftwareVersion(TOSTRING(APP_SOFTWARE_VERSION));
  HA_device.setName("HomeMaster");
  HA_device.setManufacturer("IceSoft");
  HA_device.setModel("Esp32");
  
  // This method enables availability for all device types registered on the device.
  // For example, if you have 5 sensors on the same device, you can enable
  // shared availability and change availability state of all sensors using
  // single method call "device.setAvailability(false|true)"
  //HA_device.enableSharedAvailability();

  // Optionally, you can enable MQTT LWT feature. If device will lose connection
  // to the broker, all device types related to it will be marked as offline in
  // the Home Assistant Panel.
  HA_device.enableLastWill();

  // The unique ID of each device type will be prefixed with the device's ID once enabled.
  HA_device.enableExtendedUniqueIds();
  
  uint16_t inputState = IOModule::getInstance().inputPorts;

  for(int i = 0; i < haBynaryDevicesSize; i++) {
    //in order to set custom name
    //you must use global variable, because thats the way MQTT lib is referenceing them
    //check the user and password copy below
    haBynaryDevices[i]->setName(haBynaryDevices[i]->uniqueId());
    haBynaryDevices[i]->setIcon("mdi:import");
    haBynaryDevices[i]->setCurrentState( (inputState >> i) & 0x01);
  }

  for(int i = 0; i < haLightDevicesSize; i++) {
    haLightDevices[i]->setName(haLightDevices[i]->uniqueId());
    haLightDevices[i]->onStateCommand(onStateCommand);
  }

  haTemperatureSensor.setIcon("mdi:temperature-celsius");
  haTemperatureSensor.setName("Temperature");
  haTemperatureSensor.setUnitOfMeasurement("C");

  /*
  watermeter_kitchen_cold.setName("Kitchen Cold");
  watermeter_kitchen_cold.setDeviceClass("water");
  watermeter_kitchen_cold.setStateClass("total");
  watermeter_kitchen_cold.setUnitOfMeasurement("m³");
  watermeter_kitchen_cold.setValue((float)0.0);

  watermeter_kitchen_warm.setName("Kitchen Warm");
  watermeter_kitchen_warm.setDeviceClass("water");
  watermeter_kitchen_warm.setStateClass("total");
  watermeter_kitchen_warm.setUnitOfMeasurement("m³");
  watermeter_kitchen_warm.setValue((float)0.0);

  watermeter_bath_cold.setName("Bath Cold");
  watermeter_bath_cold.setDeviceClass("water");
  watermeter_bath_cold.setStateClass("total");
  watermeter_bath_cold.setUnitOfMeasurement("m³");
  watermeter_bath_cold.setValue((float)0.0);

  watermeter_bath_warm.setName("Bath Warm");
  watermeter_bath_warm.setDeviceClass("water");
  watermeter_bath_warm.setStateClass("total");
  watermeter_bath_warm.setUnitOfMeasurement("m³");
  watermeter_bath_warm.setValue((float)0.0);
  */

  
  kitchenTemp.setName("Kitchen temperature");
  kitchenTemp.setIcon("mdi:thermometer");
  kitchenTemp.setDeviceClass("temperature");
  kitchenTemp.setUnitOfMeasurement("°C");
  kitchenTemp.setValue(0);

  livingroomTemp.setName("Livingroom temperature");
  livingroomTemp.setIcon("mdi:thermometer");
  livingroomTemp.setDeviceClass("temperature");
  livingroomTemp.setUnitOfMeasurement("°C");
  livingroomTemp.setValue(0);

  kitchenHum.setName("Kitchen humidity");
  kitchenHum.setIcon("mdi:water-percent");
  kitchenHum.setDeviceClass("humidity");
  kitchenHum.setUnitOfMeasurement("%");
  kitchenHum.setValue(0);

  livingroomHum.setName("Livingroom humidity");
  livingroomHum.setIcon("mdi:water-percent");
  livingroomHum.setDeviceClass("humidity");
  livingroomHum.setUnitOfMeasurement("%");
  livingroomHum.setValue(0);

  kitchenInfrared.setName("Kitchen infrared");
  kitchenInfrared.setIcon("mdi:remote");
  
  livingroomInfrared.setName("Livingroom infrared");
  livingroomInfrared.setIcon("mdi:remote");

  mqtt.onStateChanged(onMqttStateChanged);
  
  //Copy the username to a global variable. 
  //The mqtt lib will not work otherway     
  int strLen = DeviceSettings::getInstance().getMQTTUsername().length() + 1; // +1 for the null terminator
  mqtt_username = new char[strLen];
  DeviceSettings::getInstance().getMQTTUsername().toCharArray(mqtt_username, strLen);
  
  strLen = DeviceSettings::getInstance().getMQTTPassword().length() + 1; // +1 for the null terminator
  mqtt_password = new char[strLen];
  DeviceSettings::getInstance().getMQTTPassword().toCharArray(mqtt_password, strLen);

  mqtt.begin(DeviceSettings::getInstance().getMQTTIPAddress(), mqtt_username, mqtt_password);
}

/*
void handleRFReceiving() {
  if(!is_rf_receiving) {
    // Await packet received
    uint16_t status = startReceiving(RXpacket, RXbytes);
    if(status == PACKET_OK) {
      is_rf_receiving = true;
    }

  } else if(RXinfo.complete) {
    
    is_rf_receiving = false;

    uint16_t status = stopReceiving(RXpacket, RXbytes);

    // Update display if packet successfully received and decoded
    if (status == PACKET_OK)
    {
      // Send the received Wireless MBUS packet to the UART
      for (int i=0; i < packetSize(RXpacket[0]); i++)
      {
        if(RXpacket[i] < 16) {
            Serial.print("0");
        }
        Serial.print(RXpacket[i], HEX);
      }

      //Decode mkradio4 telegrams
      decodeTechemWaterMeters();

      Serial.println();
    }
  }
}
*/

void handleInputChange() {
  uint16_t oldState = IOModule::getInstance().oldInputPorts;
  uint16_t newState = IOModule::getInstance().inputPorts;

  //IOModule::getInstance().debug();

  for (int i = 0; i < haBynaryDevicesSize; i++)
  {
    // Extract the bit at position i for both variables
    uint8_t oldBit = (oldState >> i) & 0x01;
    uint8_t newBit = (newState >> i) & 0x01;

    // Compare the bits
    if (oldBit != newBit)
    {

#ifdef DEBUG_TO_UART
      Serial.print("Change on input: ");
      Serial.println(i);
#endif

      EventManager::getInstance().logEvent(EventManager::INPUT_CHANGE, i, newBit);

      DeviceSettings::PortEnum portType = DeviceSettings::getInstance().getPortType(i);
      int portMap = DeviceSettings::getInstance().getPortMap(i);
      
      switch (portType)
      {
        case DeviceSettings::PortEnum::SWITCH:
          //On / Off
          IOModule::getInstance().setOutput(portMap, newBit);

          if(DeviceSettings::getInstance().getMQTTEnabled()) {
            if(newBit > 0) {
              haLightDevices[portMap]->setState(true);
            } else {
              haLightDevices[portMap]->setState(false);
            }
          }
          break;
        case DeviceSettings::PortEnum::INVERTED_SWITCH:
          //Off / On
          IOModule::getInstance().setOutput(portMap, !newBit);
          if(DeviceSettings::getInstance().getMQTTEnabled()) {
            if(newBit == 0) {
              haLightDevices[portMap]->setState(true);
            } else {
              haLightDevices[portMap]->setState(false);
            }
          }
          break;
        case DeviceSettings::PortEnum::TOGGLE:
          IOModule::getInstance().toggleOutput(portMap);

          if(DeviceSettings::getInstance().getMQTTEnabled()) {
            if(haLightDevices[portMap]->getCurrentState() == false) {
              haLightDevices[portMap]->setState(true);
            } else {
              haLightDevices[portMap]->setState(false);
            }
          }
          break;
        
        case DeviceSettings::PortEnum::HA_BUTTON :
          //Do nothing to the outputs
          //just report to HA
          break;
        default:
          break;
      }

      if(DeviceSettings::getInstance().getMQTTEnabled()) {
        haBynaryDevices[i]->setState(newBit, true);
      }
    }
  }
}

void handleTempMeasurement() {


  if ((unsigned long)(millis() - previousDHTTemperatureUpdate) >= DHTtemperatureUpdateTimeout) {
    char str[8];

    //ReadDHTData(DHT_KITCHEN);
    dht_kitchen_temp = DHT_TEMP;
    dht_kitchen_hum = DHT_HUM;
    
    kitchenTemp.setValue(dht_kitchen_temp);  //dtostrf(dht_kitchen_temp, 5, 2, str));
    kitchenHum.setValue(dht_kitchen_hum);    //dtostrf(dht_kitchen_hum, 5, 2, str));

#ifdef DEBUG_TO_UART
    Serial.print("Kitchen Temp: ");    
    Serial.print(dht_kitchen_temp);    
    Serial.print("C ");    
    Serial.print(dht_kitchen_hum);    
    Serial.println("%");
#endif

    ReadDHTData(DHT_LIVING);
    dht_livingroom_temp = DHT_TEMP;
    dht_livingroom_hum = DHT_HUM;
    
    livingroomTemp.setValue(dht_livingroom_temp); //dtostrf(dht_livingroom_temp, 5, 2, str));
    livingroomHum.setValue(dht_livingroom_hum);   //dtostrf(dht_livingroom_hum, 5, 2, str));

#ifdef DEBUG_TO_UART
    Serial.print("Living Temp: ");    
    Serial.print(dht_livingroom_temp);    
    Serial.print("C ");    
    Serial.print(dht_livingroom_hum);    
    Serial.println("%");
#endif

    previousDHTTemperatureUpdate = millis();

  }


  // How much time has passed, accounting for rollover with subtraction! (rollover -> millis() overflows and reset it self to 0)
  if ((unsigned long)(millis() - previousTemperatureUpdate) >= temperatureUpdateTimeout) {
    // Get temperature
    tempSensor.requestTemperatures();
    isTempConversionInProgress = true;
  }

  // Check if enough time has passed for the conversion to complete
  if (isTempConversionInProgress && (millis() - previousTemperatureUpdate >= temperatureUpdateReadyTimeout)) {

    float temperature = tempSensor.getTempCByIndex(0);

    isTempConversionInProgress = false; // Reset the flag to allow a new conversion
#ifdef DEBUG_TO_UART
    Serial.print("Temperature: ");    
    Serial.println(temperature);
#endif
    if(temperature >= MAX_TEMPERATURE) {
      //Emergency shutdown
      for(int i = 0; i < haLightDevicesSize; i++) {
        IOModule::getInstance().setOutput(i, 0);

        if (DeviceSettings::getInstance().getMQTTEnabled()) {
          haLightDevices[i]->setBrightness(0, true);
        }
      }

      if(DeviceSettings::getInstance().getEventLogEnabled()) {
        EventManager::getInstance().logEvent(EventManager::EventType::OVERHEAT, (uint8_t) (temperature));
      }
    }

    if (DeviceSettings::getInstance().getMQTTEnabled())
    {
      haTemperatureSensor.setValue(temperature);
    }

    // Use the snapshot to set track time until next event
    previousTemperatureUpdate = millis();
  }
}

void handleInfraredReading() {
  if(digitalRead(IR_KITCHEN) == LOW) {
    if ((unsigned long)(millis() - previousIRKey) >= IRKeyTimeout) {
      detectsIRData(IR_KITCHEN);
    }
  }

  if(digitalRead(IR_LIVING) == LOW) {
    if ((unsigned long)(millis() - previousIRKey) >= IRKeyTimeout) {
      detectsIRData(IR_LIVING);
    }
  }
}

void handlePresenceDetection() {
  radar.read();
  if(radar.isConnected())
  {
    if(radar.presenceDetected())
    {
      if(radar.stationaryTargetDetected())
      {
        Serial.print(F("Stationary target: "));
        Serial.print(radar.stationaryTargetDistance());
        Serial.print(F("cm energy:"));
        Serial.print(radar.stationaryTargetEnergy());
        Serial.print(' ');
      }
      if(radar.movingTargetDetected())
      {
        Serial.print(F("Moving target: "));
        Serial.print(radar.movingTargetDistance());
        Serial.print(F("cm energy:"));
        Serial.print(radar.movingTargetEnergy());
      }
      Serial.println();
    }
    else
    {
      Serial.println(F("No target"));
    }
  }

  /*
  DEBUG
  
  if(radar.isConnected() && millis() - lastReading > 1000)  //Report every 1000ms
  {
    lastReading = millis();
    if(radar.presenceDetected())
    {
      if(radar.stationaryTargetDetected())
      {
        Serial.print(F("Stationary target: "));
        Serial.print(radar.stationaryTargetDistance());
        Serial.print(F("cm energy:"));
        Serial.print(radar.stationaryTargetEnergy());
        Serial.print(' ');
      }
      if(radar.movingTargetDetected())
      {
        Serial.print(F("Moving target: "));
        Serial.print(radar.movingTargetDistance());
        Serial.print(F("cm energy:"));
        Serial.print(radar.movingTargetEnergy());
      }
      Serial.println();
    }
    else
    {
      Serial.println(F("No target"));
    }
  }
  */
}

void setup()
{
  // Init pins
  pinMode(LAN_RESET, OUTPUT);
  pinMode(LAN_INTERRUPT, INPUT);
  pinMode(PIN_SPI_SS_ETHERNET_LIB, OUTPUT);
  pinMode(STATUS_LED, OUTPUT);
  pinMode(PIC_INTERRUPT_PIN, INPUT);

  pinMode(IR_KITCHEN, INPUT_PULLUP);
  pinMode(IR_LIVING, INPUT_PULLUP);
  pinMode(DHT_KITCHEN, INPUT);
  pinMode(DHT_LIVING, INPUT);

  pinMode(RADAR_RX_PIN, INPUT);
  pinMode(RADAR_TX_PIN, INPUT);
  pinMode(RADAR_INTERRUPT, INPUT);
  
  //pinMode(CC1101_CS, OUTPUT);
  //pinMode(CC1101_MISO, INPUT);
  //pinMode(CC1101_MOSI, OUTPUT);
  //pinMode(CC1101_CLK, OUTPUT);
  //pinMode(CC1101_GDO0, INPUT);
  //pinMode(CC1101_GDO2, INPUT);

  digitalWrite(LAN_RESET, LOW);
  
  StatusLed::begin(STATUS_LED); 

  Serial.begin(115200);
  //return;

  // Disable WiFi
  WiFi.disconnect(true);
  WiFi.mode(WIFI_OFF);

  DeviceSettings::getInstance().begin();
  //DeviceSettings::getInstance().resetPreferences();

  // Initialize pic IO module
  IOModule::getInstance().begin(PIC_RX_PIN, PIC_TX_PIN, PIC_INTERRUPT_PIN);
  IOModule::getInstance().readInputs();
  IOModule::getInstance().setDefaults();

  tempSensor.begin();

  // Wait
  delay(50);

  // Start LAN
  digitalWrite(LAN_RESET, HIGH);

  webServer.begin();
  
  // Init HA Devices
  if (DeviceSettings::getInstance().getMQTTEnabled())
  {
    initHADevices();
  }
  //IOModule::getInstance().setOutput(7, 0);
  //IOModule::getInstance().setOutput(8, 0);
  //IOModule::getInstance().setOutput(9, 0);


  RADAR_SERIAL.begin(256000, SERIAL_8N1, RADAR_RX_PIN, RADAR_TX_PIN); //UART for monitoring the radar


  if(radar.begin(RADAR_SERIAL))
  {
    Serial.println(F("OK"));
    Serial.print(F("LD2410 firmware version: "));
    Serial.print(radar.firmware_major_version);
    Serial.print('.');
    Serial.print(radar.firmware_minor_version);
    Serial.print('.');
    Serial.println(radar.firmware_bugfix_version, HEX);
  }
  else
  {
    Serial.println(F("not connected"));
  }

  /*
  if(DeviceSettings::getInstance().getRFEnabled()) {
      // Reset the CC1101 Tranceiver
    Serial.println("Resetting CC1101");
    cc1101_reset();

    // Tranceiver ID
    uint8_t id = cc1101_readReg(CC1101_PARTNUM, CC1101_STATUS_REGISTER);
    uint8_t ver = cc1101_readReg(CC1101_VERSION, CC1101_STATUS_REGISTER);

    Serial.print("CC1101 ID: ");
    Serial.print(id);
    Serial.print(" Ver: ");
    Serial.println(ver);
    
    // T-mode selected
    cc1101_initRegisters();
    Serial.println("Init CC1101 registers");
  }
  */

  DeviceAddress tempDeviceAddress;
  tempSensor.getAddress(tempDeviceAddress, 0);
  tempSensor.setResolution(tempDeviceAddress, 11);

  if(DeviceSettings::getInstance().getEventLogEnabled()) {
    EventManager::getInstance().begin();
  }
}

void loop()
{

  webServer.loop();

  if (DeviceSettings::getInstance().getMQTTEnabled())
  {
    mqtt.loop();
  }

  //if(DeviceSettings::getInstance().getRFEnabled()) {
    //handleRFReceiving();
  //}

  if (IOModule::getInstance().checkInputs())
  {
    handleInputChange();
  }

  handleInfraredReading();

  handleTempMeasurement();

  handlePresenceDetection();

  StatusLed::update(); 
}