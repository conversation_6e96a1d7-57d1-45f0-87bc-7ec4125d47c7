#include "deviceSettings.h"
#include <esp_mac.h>



DeviceSettings::DeviceSettings() : preferences() {}

DeviceSettings& DeviceSettings::getInstance() {
  static DeviceSettings instance;
  return instance;
}

void DeviceSettings::begin() {
  preferences.begin("device_settings", false); // Use "device_settings" as the namespace

  app_version = preferences.getUInt("app_version", 0);

#ifdef DEBUG_TO_UART 
  Serial.print("DeviceSettings::app_version = ");
  Serial.println(app_version);
#endif

  if (app_version == 0) {
    // Set default values if version is 0
    setDefaultValues();
  } else if(app_version != APP_SOFTWARE_VERSION) {
    //New firmware update
    app_version = APP_SOFTWARE_VERSION;
    preferences.putUInt("app_version", app_version);
  }

  loadAllValues();
}



byte DeviceSettings::getAppVersion() const {
  return app_version;
}

bool DeviceSettings::getCanbusEnabled() const {
  return canbus_enabled;
}

bool DeviceSettings::getMQTTEnabled() const {
  return mqtt_enabled;
}

bool DeviceSettings::getEventLogEnabled() const {
  return eventlog_enabled;
}

bool DeviceSettings::getDHCPEnabled() const {
  return dhcp_enabled;
}

String DeviceSettings::getDeviceName() const {
  return device_name;
}

byte* DeviceSettings::getDeviceMACAddress() const {
  return const_cast<byte*>(device_mac);
}

IPAddress DeviceSettings::getMQTTIPAddress() const {
  return mqtt_ip;
}

String DeviceSettings::getMQTTUsername() const {
  return mqtt_username;
}

String DeviceSettings::getMQTTPassword() const {
  return mqtt_password;
}

byte* DeviceSettings::getMQTTDeviceID() const {
  return const_cast<byte*>(mqtt_device_id);
}

DeviceSettings::PortEnum DeviceSettings::getPortType(int index) const {
  return portTypes[index];
}

int DeviceSettings::getPortMap(int index) const {
  return portMaps[index];
}

String DeviceSettings::getPortName(int index) const {
  return portNames[index];
}


int DeviceSettings::getOutputDebounceTime() const {
  return outputDebounceTime;
}


IPAddress DeviceSettings::getStaticIP() const {
  return static_ip;
}

IPAddress DeviceSettings::getDNSIP() const {
  return dns_ip;
}

IPAddress DeviceSettings::getGatewayIP() const {
  return gateway_ip;
}

IPAddress DeviceSettings::getSubnetIP() const {
  return subnet_ip;
}



void DeviceSettings::setAppVersion(byte version) {
  app_version = version;
  preferences.putUInt("app_version", version);
}

void DeviceSettings::setCanbusEnabled(bool value) {
  canbus_enabled = value;
  preferences.putBool("canbus_enabled", value);
}

void DeviceSettings::setMQTTEnabled(bool value) {
  mqtt_enabled = value;
  preferences.putBool("mqtt_enabled", value);
}

void DeviceSettings::setEventLogEnabled(bool value) {
  eventlog_enabled = value;
  preferences.putBool("event_enabled", value);
}

void DeviceSettings::setDHCPEnabled(bool value) {
  dhcp_enabled = value;
  preferences.putBool("dhcp_enabled", value);
}

void DeviceSettings::setDeviceName(String name) {
  device_name = name;
  preferences.putString("device_name", name);
}

void DeviceSettings::setDeviceMACAddress(byte* mac) {
  memcpy(device_mac, mac, 6);
  preferences.putBytes("device_mac", mac, 6);
}

void DeviceSettings::setMQTTIPAddress(IPAddress ip) {
  mqtt_ip = ip;
  preferences.putString("mqtt_ip", ip.toString().c_str());
}

void DeviceSettings::setMQTTUsername(String username) {
  mqtt_username = username;
  preferences.putString("mqtt_username", username);
}

void DeviceSettings::setMQTTPassword(String password) {
  mqtt_password = password;
  preferences.putString("mqtt_password", password);
}

void DeviceSettings::setMQTTDeviceID(byte* deviceID) {
  memcpy(mqtt_device_id, deviceID, 6);
  preferences.putBytes("mqtt_device_id", deviceID, 6);
}

void DeviceSettings::setPortType(int index, PortEnum port) {
  portTypes[index] = port;
  
  char keyBuffer[15];
  sprintf(keyBuffer, "portType_%d", index);

  preferences.putUInt(keyBuffer, static_cast<uint32_t>(port));
}

void DeviceSettings::setPortMap(int index, int outputPort) {
  portMaps[index] = outputPort;
  
  char keyBuffer[15];
  sprintf(keyBuffer, "portMap_%d", index);

  preferences.putUInt(keyBuffer, outputPort);
}

void DeviceSettings::setPortName(int index, String name) {
  portNames[index] = name;
  
  char keyBuffer[15];
  sprintf(keyBuffer, "portName_%d", index);

  preferences.putString(keyBuffer, name);
}

void DeviceSettings::setOutputDebounceTime(uint16_t value) {
  outputDebounceTime = value;
  preferences.putUInt("btnDebounce", value);
}

void DeviceSettings::setPowerMonitoringEnabled(bool enabled) {
  powerMonitoringEnabled = enabled;
  preferences.putBool("powerMonitor", enabled);
}

bool DeviceSettings::isEnergyMonitoringEnabled() const {
  return powerMonitoringEnabled;
}

void DeviceSettings::setOutputPowerConsumption(int index, float watts) {
  if (index >= 0 && index < NUM_OUTPUTS) {
    outputEnergyConsumption[index] = watts;
    char keyBuffer[20];
    sprintf(keyBuffer, "power_%d", index);
    preferences.putFloat(keyBuffer, watts);
  }
}

float DeviceSettings::getOutputEnergyConsumption(int index) const {
  if (index >= 0 && index < NUM_OUTPUTS) {
    return outputEnergyConsumption[index];
  }
  return 0.0;
}



void DeviceSettings::setStaticIP(IPAddress ip) {
  static_ip = ip;
  preferences.putString("static_ip", ip.toString().c_str());
}

void DeviceSettings::setDNSIP(IPAddress ip) {
  dns_ip = ip;
  preferences.putString("dns_ip", ip.toString().c_str());
}

void DeviceSettings::setGatewayIP(IPAddress ip) {
  gateway_ip = ip;
  preferences.putString("gateway_ip", ip.toString().c_str());
}

void DeviceSettings::setSubnetIP(IPAddress ip) {
  subnet_ip = ip;
  preferences.putString("subnet_ip", ip.toString().c_str());
}


void DeviceSettings::resetPreferences() {
  preferences.clear();
  setDefaultValues();
}


void DeviceSettings::setDefaultValues() {

#ifdef DEBUG_TO_UART 
  Serial.println("DeviceSettings::setDefaultValues");
#endif

  app_version = APP_SOFTWARE_VERSION;
  preferences.putUInt("app_version", app_version);

  // Set your default values here
  setCanbusEnabled(false);
  setMQTTEnabled(false);
  setEventLogEnabled(false);
  setDHCPEnabled(true);
  
  // Set other default values as needed

  //Get the burned in MAC
  esp_efuse_mac_get_default(device_mac);
  preferences.putBytes("device_mac", device_mac, 6);
  preferences.putBytes("mqtt_device_id", device_mac, 6);


  //DELETE ME
  setMQTTEnabled(true);
  setDHCPEnabled(false);
  setStaticIP(IPAddress(192,168,1,238));
  setDNSIP(IPAddress(192,168,1,1));
  setGatewayIP(IPAddress(192,168,1,1));
  setSubnetIP(IPAddress(192,168,1,1));
  setMQTTIPAddress(IPAddress(192,168,1,223));
  setMQTTUsername(String("homeassistant"));
  setMQTTPassword(String("ohfohPheocaToiN9ahpheiphahphooZoas5iengua9thachie1Zoh4dooSieshi6"));
  //DELETE ME

}

void DeviceSettings::loadAllValues() {

#ifdef DEBUG_TO_UART 
  Serial.println("DeviceSettings::loadAllValues");
#endif

  canbus_enabled = preferences.getBool("canbus_enabled", false);
  mqtt_enabled = preferences.getBool("mqtt_enabled", false);
  eventlog_enabled = preferences.getBool("event_enabled", false);
  dhcp_enabled = preferences.getBool("dhcp_enabled", true); 

  device_name = preferences.getString("device_name", "");

  // Read the device MAC address
  preferences.getBytes("device_mac", device_mac, 6);

  // Assuming each port takes 1 byte for port enum
  for (int i = 0; i < NUM_INPUTS; ++i) {
    char keyBuffer[15];
    sprintf(keyBuffer, "portType_%d", i);
    portTypes[i] = static_cast<PortEnum>(preferences.getUInt(keyBuffer, 0));

    sprintf(keyBuffer, "portMap_%d", i);
    portMaps[i] = preferences.getUInt(keyBuffer, i);

    sprintf(keyBuffer, "portName_%d", i);
    portNames[i] = preferences.getString(keyBuffer, "Input");
  }

  outputDebounceTime = preferences.getUInt("btnDebounce", 10);

  mqtt_ip.fromString(preferences.getString("mqtt_ip", ""));
  mqtt_username = preferences.getString("mqtt_username", "");
  mqtt_password = preferences.getString("mqtt_password", "");
  
  // Read the MQTT device id
  preferences.getBytes("mqtt_device_id", mqtt_device_id, 6);

  // New IP address fields
  static_ip.fromString(preferences.getString("static_ip", ""));
  dns_ip.fromString(preferences.getString("dns_ip", ""));
  gateway_ip.fromString(preferences.getString("gateway_ip", ""));
  subnet_ip.fromString(preferences.getString("subnet_ip", ""));

  powerMonitoringEnabled = preferences.getBool("powerMonitor", false);
  
  for (int i = 0; i < NUM_OUTPUTS; i++) {
    char keyBuffer[20];
    sprintf(keyBuffer, "power_%d", i);
    outputEnergyConsumption[i] = preferences.getFloat(keyBuffer, 0.0);
  }
}
