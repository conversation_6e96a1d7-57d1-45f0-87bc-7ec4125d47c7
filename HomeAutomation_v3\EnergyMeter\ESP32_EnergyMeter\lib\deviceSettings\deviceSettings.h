#ifndef DEVICE_SETTINGS_H
#define DEVICE_SETTINGS_H

#include <Arduino.h>
#include <Preferences.h>
#include <IPAddress.h>
#include "global_config.h"


class DeviceSettings {

public:

  // Singleton instance
  static DeviceSettings& getInstance();

  // Begin method to read settings from Preferences
  void begin();

  // Method to get stored values
  byte getAppVersion() const;
  
  bool getMQTTEnabled() const; 
  bool getEventLogEnabled() const;
  bool getDHCPEnabled() const; 
  String getDeviceName() const;
  byte* getDeviceMACAddress() const; // Return pointer to the MAC address array
  IPAddress getMQTTIPAddress() const;
  String getMQTTUsername() const;
  String getMQTTPassword() const;
  byte* getMQTTDeviceID() const;

  float getEnergyDatalogPeriod() const;
  float getEnergyVoltageCalibration(int index) const;
  float getEnergyVoltagePhaseShift(int index) const;
  String getEnergyCurrentLables(int index) const;
  bool getEnergyCurrentEnabled(int index) const;
  float getEnergyCurrentCalibration(int index) const;
  float getEnergyCurrentPhaseShift(int index) const;
  int getEnergyCurrentMap(int index) const;

  IPAddress getStaticIP() const;
  IPAddress getDNSIP() const;
  IPAddress getGatewayIP() const;
  IPAddress getSubnetIP() const;

  // Method to change a value in Preferences
  void setAppVersion(byte version);
  void setMQTTEnabled(bool value);
  void setEventLogEnabled(bool value);
  void setDHCPEnabled(bool value);
  void setDeviceName(String name);
  void setDeviceMACAddress(byte* mac);
  void setMQTTIPAddress(IPAddress ip);
  void setMQTTUsername(String username);
  void setMQTTPassword(String password);
  void setMQTTDeviceID(byte* deviceID);
  
  void setEnergyDatalogPeriod(float value);
  void setEnergyVoltageCalibration(int index, float value);
  void setEnergyVoltagePhaseShift(int index, float value);
  void setEnergyCurrentLables(int index, String value);
  void setEnergyCurrentEnabled(int index, bool value);
  void setEnergyCurrentCalibration(int index, float value);
  void setEnergyCurrentPhaseShift(int index, float value);
  void setEnergyCurrentMap(int index, int value);  

  void setStaticIP(IPAddress ip);
  void setDNSIP(IPAddress ip);
  void setGatewayIP(IPAddress ip);
  void setSubnetIP(IPAddress ip);

  
  void resetPreferences();
  
  // Backup and Restore functions
  String backupPreferences();
  void restorePreferences(const String& backupString);

private:
  // Private constructor to enforce singleton pattern
  DeviceSettings();

  // Added method for setting default values
  void setDefaultValues();
  void loadAllValues();

  // Private member variables
  Preferences preferences;
  byte app_version;
  bool mqtt_enabled;
  bool eventlog_enabled;
  bool dhcp_enabled;
  String device_name;
  byte device_mac[6];
  IPAddress mqtt_ip;
  String mqtt_username;
  String mqtt_password;
  byte mqtt_device_id[6];
  IPAddress static_ip;
  IPAddress dns_ip;
  IPAddress gateway_ip;
  IPAddress subnet_ip;
  

  float energyDatalogPeriod;
  float energyVoltageCalibration[3];
  float energyVoltagePhaseShift[3];

  String energyCurrentLables[NUM_INPUTS];
  bool energyCurrentEnabled[NUM_INPUTS];
  float energyCurrentCalibration[NUM_INPUTS];
  float energyCurrentPhaseShift[NUM_INPUTS];
  
  int energyCurrentMap[NUM_INPUTS];

};

#endif // DEVICE_SETTINGS_H
