#ifndef EEPROM_H
#define EEPROM_H

#include <xc.h>
#include <stdint.h>

// EEPROM Constants
#define EEPROM_BLOCK_SIZE 4
#define EEPROM_SIZE 1024
#define NUM_BLOCKS (EEPROM_SIZE / EEPROM_BLOCK_SIZE)
#define MAGIC_NUMBER 0xAA
#define EEPROM_INVALID_MARKER 0xFF

// Function declarations
uint8_t EEPROM_Read(uint16_t address);
void EEPROM_Write(uint16_t address, uint8_t data);
uint8_t calculateChecksum(uint16_t data);
uint16_t findLatestBlock(void);
void saveOutputStates(void);
uint16_t loadOutputStates(void);
void restoreOutputStates(void);

#endif // EEPROM_H