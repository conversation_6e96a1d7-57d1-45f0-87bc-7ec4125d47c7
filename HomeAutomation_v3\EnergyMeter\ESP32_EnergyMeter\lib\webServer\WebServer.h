/*
 * Custom web server class that handles web request over ethernet 
 * Author: <PERSON>
 * 
 * Ethernet is https://www.arduino.cc/reference/en/libraries/ethernet/  version 2.0.2 
 * but with edited Ethernet.h Have to add on line 263 
 * void begin(uint16_t port) {	_port = port;	}
 */

#ifndef WebServer_lib
#define WebServer_lib

#include <Arduino.h>
#include <SPI.h>
#include <Update.h>
#include "Ethernet.h"
#include "global_config.h"
#include "deviceSettings.h"
#include "ioModule.h"
#include "EventManager.h"
#include "statusLed.h"
#include "index_html_gz.h"
#include "HADevice.h"
extern HADevice HA_device;

class WebServer {
public:
    WebServer(uint8_t serverPort);
    void begin();
    void loop();
    //void debug();
    
    // Add this line to make temperature accessible
    static float temperature;  // Temperature value shared across the application
    // Static variable to hold the local IP address once connected
    static IPAddress localIP;  // Local IP address shared across the application
    static char localURL[25];

    uint8_t* _deviceMacAddress;

    EthernetServer server{80};
private:
    uint8_t _port;
    uint8_t _initialezed;

    void handleFirmwareUpload(EthernetClient client);
    void handleFileUpload(EthernetClient client);

    void handleIndexFile(EthernetClient client);
    void handleAjaxInit(EthernetClient client);
    void handleAjaxSettings(EthernetClient client);
    void handleAjaxDebug(EthernetClient client);

    //void handleAjaxSaveMQTT(EthernetClient client);
    //void handleAjaxSaveNetwork(EthernetClient client);
    //void handleAjaxSaveCanBus(EthernetClient client);
    //void handleAjaxSaveEventlog(EthernetClient client);
    //void handleAjaxSavePorts(EthernetClient client);
    //void handleAjaxSaveOutputPorts(EthernetClient client);

    void handleAjaxSetOutput(EthernetClient client);

    int splitString(String, char, String*, int);
    String urlDecode(String);
    int hexCharToint(char);

    // New generic save method
    void handleAjaxSaveSettings(EthernetClient client);
    
    // New export/import methods
    void handleSettingsExport(EthernetClient client);
    void handleSettingsImport(EthernetClient client);
};

#endif
