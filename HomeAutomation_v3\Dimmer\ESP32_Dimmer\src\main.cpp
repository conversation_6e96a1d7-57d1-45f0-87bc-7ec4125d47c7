#include <Arduino.h>
#include <WiFi.h>
#include <Wire.h>
#include <ACAN_ESP32.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <ArduinoHA.h>
#include "global_config.h"
#include "eventManager.h"
#include "deviceSettings.h"
#include "WebServer.h"
#include "ioModule.h"
#include "statusLed.h"



EthernetClient mqqtClient;

WebServer webServer(80);

HADevice HA_device;
HAMqtt mqtt(mqqtClient, HA_device, 32);

char *mqtt_username;
char *mqtt_password;
bool mqtt_connected = false;

HABinarySensor haSensorInput1("Input1");
HABinarySensor haSensorInput2("Input2");
HABinarySensor haSensorInput3("Input3");
HABinarySensor haSensorInput4("Input4");
HABinarySensor haSensorInput5("Input5");
HABinarySensor haSensorInput6("Input6");
HABinarySensor haSensorInput7("Input7");
HABinarySensor haSensorInput8("Input8");
HABinarySensor haSensorInput9("Input9");
HABinarySensor haSensorInput10("Input10");
HABinarySensor haSensorInput11("Input11");
HABinarySensor haSensorInput12("Input12");

HALight haLightOutput1("Output1", HALight::BrightnessFeature);
HALight haLightOutput2("Output2", HALight::BrightnessFeature);
HALight haLightOutput3("Output3", HALight::BrightnessFeature);
HALight haLightOutput4("Output4", HALight::BrightnessFeature);
HALight haLightOutput5("Output5", HALight::BrightnessFeature);

HASensorNumber haTemperatureSensor("Temperature", HASensorNumber::PrecisionP2);

HABinarySensor* haBynaryDevices[] = {&haSensorInput1, &haSensorInput2, &haSensorInput3, &haSensorInput4, &haSensorInput5, &haSensorInput6, &haSensorInput7, &haSensorInput8, &haSensorInput9, &haSensorInput10, &haSensorInput11, &haSensorInput12};
HALight*        haLightDevices[] = {&haLightOutput1, &haLightOutput2, &haLightOutput3, &haLightOutput4, &haLightOutput5};

OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature tempSensor(&oneWire);

//long timerUpdateOutput = 0;
//uint8_t outputCurrentId = 0;
//uint8_t outputCurrentState = 0;

// CANbus
static uint32_t gBlinkLedDate = 0;
static uint8_t gReceivedFrameCount = 0;
static uint32_t gSentFrameCount = 0;

const int temperatureUpdateTimeout = 30000; //in ms
const int temperatureUpdateReadyTimeout =  temperatureUpdateTimeout + 750; //in ms
unsigned long previousTemperatureUpdate = 0;
bool isTempConversionInProgress = false;

//Update hold buttons
const int buttonHoldUpdateTimeout = 100; //in ms
unsigned long previousButtonHoldUpdate = 0;
uint8_t buttonHoldEnable = 0;


void onMqttStateChanged(HAMqtt::ConnectionState state) {
    int8_t int_state = static_cast<int8_t>(state);

    switch (int_state)
    {
      case -5:
        Serial.println(F("MQTT state changed to: StateConnecting"));
        mqtt_connected = false;
        break;
      case -4:
        Serial.println(F("MQTT state changed to: StateConnectionTimeout"));
        mqtt_connected = false;
        break;
      case -3:
        Serial.println(F("MQTT state changed to: StateConnectionLost"));
        mqtt_connected = false;
        break;
      case -2:
        Serial.println(F("MQTT state changed to: StateConnectionFailed"));
        mqtt_connected = false;
        break;
      case -1:
        Serial.println(F("MQTT state changed to: StateDisconnected"));
        mqtt_connected = false;
        break;
      case 0:
        Serial.println(F("MQTT state changed to: StateConnected"));
        mqtt_connected = true;
        break;
      case 1:
        Serial.println(F("MQTT state changed to: StateBadProtocol"));
        mqtt_connected = false;
        break;
      case 2:
        Serial.println(F("MQTT state changed to: StateBadClientId"));
        mqtt_connected = false;
        break;
      case 3:
        Serial.println(F("MQTT state changed to: StateUnavailable"));
        mqtt_connected = false;
        break;
      case 4:
        Serial.println(F("MQTT state changed to: StateBadCredentials"));
        mqtt_connected = false;
        break;
      case 5:
        Serial.println(F("MQTT state changed to: StateUnauthorized"));
        mqtt_connected = false;
        break;
      default:
        break;
    }

    Serial.println(static_cast<int8_t>(state));
}

uint16_t mapBrightnessToChannel(uint8_t percent, uint8_t channel) {
  uint16_t map_brightness = 0;
  if(percent == 0) {
    map_brightness = 0;
  } else if(percent == 100) {
    map_brightness = DIMMER_MAX_STEP;
  } else {
    map_brightness = map(percent, 0, 100, DeviceSettings::getInstance().getOutputMin(channel), DeviceSettings::getInstance().getOutputMax(channel));
  }
  return map_brightness;
}


void onStateCommand(bool state, HALight* sender) {
    Serial.print("State: ");
    Serial.println(state);

    for(int i = 0; i < NUM_OUTPUTS; i++) {
      if(haLightDevices[i] == sender) {
        if(state == true) {
          if(haLightDevices[i]->getCurrentBrightness() == 0) {
            haLightDevices[i]->setCurrentBrightness(100);
          }
          
          IOModule::getInstance().setDimmer(i, mapBrightnessToChannel(haLightDevices[i]->getCurrentBrightness(), i));
        } else {
          IOModule::getInstance().setDimmer(i, 0);
        }

        if(DeviceSettings::getInstance().getEventLogEnabled()) {
          EventManager::getInstance().logEvent(EventManager::EventType::MQTT_STATE, i, (state == true)? 1 : 0);
        }
        break;
      }
    }

    sender->setState(state); // report state back to the Home Assistant
}

void onBrightnessCommand(uint8_t brightness, HALight* sender) {
    Serial.print("Brightness: ");
    Serial.print(brightness);
    Serial.println("%");

    for(int i = 0; i < NUM_OUTPUTS; i++) {
      if(haLightDevices[i] == sender) {
        IOModule::getInstance().setDimmer(i, mapBrightnessToChannel(brightness, i));

        if(DeviceSettings::getInstance().getEventLogEnabled()) {
          EventManager::getInstance().logEvent(EventManager::EventType::MQTT_BRIGHTNESS, i, brightness);
        }
        
        break;
      }
    }

    sender->setBrightness(brightness); // report brightness back to the Home Assistant
}


void initCanbus() {

#ifdef DEBUG_TO_UART
  Serial.println("INIT - Can Bus");
#endif

  ACAN_ESP32_Settings settings(500 * 1000); // 125 kbit/s
  settings.mRequestedCANMode = ACAN_ESP32_Settings::NormalMode;
  settings.mRxPin = CAN_RX_PIN;
  settings.mTxPin = CAN_TX_PIN;

  const uint32_t errorCode = ACAN_ESP32::can.begin(settings);
#ifdef DEBUG_TO_UART
  if (errorCode == 0)
  {
    Serial.print("Bit Rate prescaler: ");
    Serial.println(settings.mBitRatePrescaler);
    Serial.print("Time Segment 1:     ");
    Serial.println(settings.mTimeSegment1);
    Serial.print("Time Segment 2:     ");
    Serial.println(settings.mTimeSegment2);
    Serial.print("RJW:                ");
    Serial.println(settings.mRJW);
    Serial.print("Triple Sampling:    ");
    Serial.println(settings.mTripleSampling ? "yes" : "no");
    Serial.print("Actual bit rate:    ");
    Serial.print(settings.actualBitRate());
    Serial.println(" bit/s");
    Serial.print("Exact bit rate ?    ");
    Serial.println(settings.exactBitRate() ? "yes" : "no");
    Serial.print("Distance            ");
    Serial.print(settings.ppmFromDesiredBitRate());
    Serial.println(" ppm");
    Serial.print("Sample point:       ");
    Serial.print(settings.samplePointFromBitStart());
    Serial.println("%");
    Serial.println("Configuration OK!");
  }
  else
  {
    Serial.print("Configuration error 0x");
    Serial.println(errorCode, HEX);
  }
#endif
}

void initHADevices() {

#ifdef DEBUG_TO_UART
  Serial.println("INIT - HA Devices");
#endif

  const byte *deviceId = DeviceSettings::getInstance().getMQTTDeviceID();

  HA_device.setUniqueId(deviceId, 6);
  HA_device.setSoftwareVersion(TOSTRING(APP_SOFTWARE_VERSION));
  HA_device.setName("Dimmer");
  HA_device.setManufacturer("IceSoft");
  HA_device.setModel("DIN - 5ch dimmer");
  HA_device.setConfigurationUrl(webServer.localURL);

  // This method enables availability for all device types registered on the device.
  // For example, if you have 5 sensors on the same device, you can enable
  // shared availability and change availability state of all sensors using
  // single method call "device.setAvailability(false|true)"
  //HA_device.enableSharedAvailability();

  // Optionally, you can enable MQTT LWT feature. If device will lose connection
  // to the broker, all device types related to it will be marked as offline in
  // the Home Assistant Panel.
  HA_device.enableLastWill();

  // The unique ID of each device type will be prefixed with the device's ID once enabled.
  HA_device.enableExtendedUniqueIds();

  
  uint16_t inputState = IOModule::getInstance().inputPorts;


  for(int i = 0; i < NUM_INPUTS; i++) {
    //in order to set custom name
    //you must use global variable, because thats the way MQTT lib is referenceing them
    //check the user and password copy below
    haBynaryDevices[i]->setName(haBynaryDevices[i]->uniqueId());
    haBynaryDevices[i]->setIcon("mdi:import");
    haBynaryDevices[i]->setCurrentState( (inputState >> i) & 0x01);
  }

  for(int i = 0; i < NUM_OUTPUTS; i++) {
    haLightDevices[i]->setName(haLightDevices[i]->uniqueId());
    haLightDevices[i]->setBrightnessScale(100);
    haLightDevices[i]->onStateCommand(onStateCommand);
    haLightDevices[i]->onBrightnessCommand(onBrightnessCommand);     
  }

  haTemperatureSensor.setIcon("mdi:temperature-celsius");
  haTemperatureSensor.setName("Temperature");
  haTemperatureSensor.setUnitOfMeasurement("C");
  
  mqtt.onStateChanged(onMqttStateChanged);
  
  //Copy the username to a global variable. 
  //The mqtt lib will not work otherway     
  int strLen = DeviceSettings::getInstance().getMQTTUsername().length() + 1; // +1 for the null terminator
  mqtt_username = new char[strLen];
  DeviceSettings::getInstance().getMQTTUsername().toCharArray(mqtt_username, strLen);
  
  strLen = DeviceSettings::getInstance().getMQTTPassword().length() + 1; // +1 for the null terminator
  mqtt_password = new char[strLen];
  DeviceSettings::getInstance().getMQTTPassword().toCharArray(mqtt_password, strLen);

  mqtt.begin(DeviceSettings::getInstance().getMQTTIPAddress(), mqtt_username, mqtt_password);
}


void handleInputChange() {
  uint16_t oldState = IOModule::getInstance().oldInputPorts;
  uint16_t newState = IOModule::getInstance().inputPorts;

  //IOModule::getInstance().debug();

  buttonHoldEnable = 0;

  for (int i = 0; i < NUM_INPUTS; i++)
  {
    // Extract the bit at position i for both variables
    uint8_t oldBit = (oldState >> i) & 0x01;
    uint8_t newBit = (newState >> i) & 0x01;

    // Compare the bits
    if (oldBit != newBit)
    {

#ifdef DEBUG_TO_UART
      Serial.print("Change on input: ");
      Serial.println(i);
#endif

      EventManager::getInstance().logEvent(EventManager::INPUT_CHANGE, i, newBit);

      DeviceSettings::PortEnum portType = DeviceSettings::getInstance().getPortType(i);
      int portMap = DeviceSettings::getInstance().getPortMap(i);
      
      switch (portType)
      {
        case DeviceSettings::PortEnum::SWITCH:
          //On / Off
          IOModule::getInstance().setOutput(portMap, newBit);

          if(DeviceSettings::getInstance().getMQTTEnabled()) {
            if(newBit > 0) {
              haLightDevices[portMap]->setBrightness(100, true);
              haLightDevices[portMap]->setState(true);
            } else {
              haLightDevices[portMap]->setBrightness(0);
              haLightDevices[portMap]->setState(false);
            }
          }
          break;
        case DeviceSettings::PortEnum::INVERTED_SWITCH:
          //Off / On
          IOModule::getInstance().setOutput(portMap, !newBit);
          if(DeviceSettings::getInstance().getMQTTEnabled()) {
            if(newBit == 0) {
              haLightDevices[portMap]->setBrightness(100, true);
              haLightDevices[portMap]->setState(true);
            } else {
              haLightDevices[portMap]->setBrightness(0);
              haLightDevices[portMap]->setState(false);
            }
          }
          break;
        case DeviceSettings::PortEnum::TOGGLE:
          IOModule::getInstance().toggleOutput(portMap);

          if(DeviceSettings::getInstance().getMQTTEnabled()) {
            if(haLightDevices[portMap]->getCurrentState() == false) {
              haLightDevices[portMap]->setBrightness(100, true);
              haLightDevices[portMap]->setState(true);
            } else {
              haLightDevices[portMap]->setBrightness(0);
              haLightDevices[portMap]->setState(false);
            }
          }
          break;

        case DeviceSettings::PortEnum::DIM_UP:
        case DeviceSettings::PortEnum::DIM_DOWN:
        //case DeviceSettings::PortEnum::DIM_ONE_BUTTON:
          buttonHoldEnable = newBit;
          previousButtonHoldUpdate = millis();
          break;

        case DeviceSettings::PortEnum::HA_BUTTON :
          //Do nothing to the outputs
          //just report to HA
          break;
        default:
          break;
      }

      if(DeviceSettings::getInstance().getMQTTEnabled()) {
        haBynaryDevices[i]->setState(newBit, true);
      }
    }
  }
}

void handleInputDimming() {

  // How much time has passed, accounting for rollover with subtraction! (rollover -> millis() overflows and reset it self to 0)
  if (buttonHoldEnable && (unsigned long)(millis() - previousButtonHoldUpdate) >= buttonHoldUpdateTimeout) {

    uint16_t inputState = IOModule::getInstance().inputPorts;

    for (int i = 0; i < NUM_INPUTS; i++)
    {
      // Extract the bit at position i for both variables
      uint8_t currentBit = (inputState >> i) & 0x01;

      if(currentBit == 0) {
        //Button not pressed
        continue;
      }

      DeviceSettings::PortEnum portType = DeviceSettings::getInstance().getPortType(i);
      int portMap = DeviceSettings::getInstance().getPortMap(i);
      int portMin = DeviceSettings::getInstance().getOutputMin(portMap);
      int portMax = DeviceSettings::getInstance().getOutputMax(portMap);
      uint16_t brightnessLevel = IOModule::getInstance().getDimmer(portMap);

      if(portType == DeviceSettings::PortEnum::DIM_UP) {
        brightnessLevel += DIMMER_BUTTON_STEP;

        //We want to go UP -> check if we are below MIN
        if(brightnessLevel < portMin) {
          //Jump straight to min
          brightnessLevel = portMin;
        }

        //Check if we are above MAX
        if(brightnessLevel > portMax) {
          //Jump straight to max
          brightnessLevel = DIMMER_MAX_STEP;
        }
      
      } else if(portType == DeviceSettings::PortEnum::DIM_DOWN) {
        
        if(brightnessLevel >= DIMMER_BUTTON_STEP) {
          brightnessLevel -= DIMMER_BUTTON_STEP;
        } else {
          brightnessLevel = 0;
        }

        //We want to go Down -> check if we are below MIN
        if(brightnessLevel < portMin) {
          //Jump straight to 0
          brightnessLevel = 0;
        }

        //Check if we are above MAX
        if(brightnessLevel > portMax) {
          //Jump straight to max
          brightnessLevel = portMax;
        }
   
      }
      // else if(portType == DeviceSettings::PortEnum::DIM_ONE_BUTTON) {}

      IOModule::getInstance().setDimmer(portMap, brightnessLevel);

      if(DeviceSettings::getInstance().getMQTTEnabled()) {
        //Prevent division by zero
        if(brightnessLevel > 0) {
          float HA_Level = (brightnessLevel / (DIMMER_MAX_STEP * 1.0)) * 100.0;
          haLightDevices[portMap]->setBrightness( (uint8_t)HA_Level, true);
          haLightDevices[portMap]->setState(true);
        } else {
          haLightDevices[portMap]->setBrightness(0);
          haLightDevices[portMap]->setState(false);
        }
      }
    }
   
    // Use the snapshot to set track time until next event
    previousButtonHoldUpdate = millis();
  }
}

void handleTempMeasurement() {
  // How much time has passed, accounting for rollover with subtraction! (rollover -> millis() overflows and reset it self to 0)
  if ((unsigned long)(millis() - previousTemperatureUpdate) >= temperatureUpdateTimeout) {
    // Get temperature
    tempSensor.requestTemperatures();
    isTempConversionInProgress = true;
  }

  // Check if enough time has passed for the conversion to complete
  if (isTempConversionInProgress && (millis() - previousTemperatureUpdate >= temperatureUpdateReadyTimeout)) {

    float temperature = tempSensor.getTempCByIndex(0);

    isTempConversionInProgress = false; // Reset the flag to allow a new conversion
#ifdef DEBUG_TO_UART
    Serial.print("Temperature: ");    
    Serial.println(temperature);
#endif
    if(temperature >= MAX_TEMPERATURE) {
      //Emergency shutdown
      for(int i = 0; i < NUM_OUTPUTS; i++) {
        IOModule::getInstance().setDimmer(i, 0);

        if (DeviceSettings::getInstance().getMQTTEnabled()) {
          haLightDevices[i]->setBrightness(0, true);
        }
      }

      if(DeviceSettings::getInstance().getEventLogEnabled()) {
        EventManager::getInstance().logEvent(EventManager::EventType::OVERHEAT, (uint8_t) (temperature));
      }
    }

    if (DeviceSettings::getInstance().getMQTTEnabled())
    {
      haTemperatureSensor.setValue(temperature);
    }

    // Use the snapshot to set track time until next event
    previousTemperatureUpdate = millis();
  }

}


void setup()
{
  // Init pins
  pinMode(LAN_RESET, OUTPUT);
  pinMode(LAN_INTERRUPT, INPUT);
  pinMode(STATUS_LED, OUTPUT);
  pinMode(PIC_INTERRUPT_PIN, INPUT);

  digitalWrite(LAN_RESET, LOW);
  
  StatusLed::begin(STATUS_LED); 

  Serial.begin(115200);

  // Disable WiFi
  WiFi.disconnect(true);
  WiFi.mode(WIFI_OFF);

  DeviceSettings::getInstance().begin();
  //DeviceSettings::getInstance().resetPreferences();

  // Initialize pic IO module
  IOModule::getInstance().begin(PIC_RX_PIN, PIC_TX_PIN, PIC_INTERRUPT_PIN);
  IOModule::getInstance().readInputs();
  IOModule::getInstance().setDefaults();

  tempSensor.begin();

  // Wait
  delay(50);

  // Start LAN
  digitalWrite(LAN_RESET, HIGH);

  webServer.begin();

  // Init CAN
  if (DeviceSettings::getInstance().getCanbusEnabled())
  {
    initCanbus();
  }

  // Init HA Devices
  if (DeviceSettings::getInstance().getMQTTEnabled())
  {
    initHADevices();
  }

  DeviceAddress tempDeviceAddress;
  tempSensor.getAddress(tempDeviceAddress, 0);
  tempSensor.setResolution(tempDeviceAddress, 11);

  if(DeviceSettings::getInstance().getEventLogEnabled()) {
    EventManager::getInstance().begin();
  }

}

void handleCAN()
{
  CANMessage frame;
  if (gBlinkLedDate < millis())
  {
    gBlinkLedDate += 1500;
    // digitalWrite (MOC9, !digitalRead (MOC9)) ;
    //  Serial.print ("Sent: ") ;
    //  Serial.print (gSentFrameCount) ;
    //  Serial.print (" ") ;
    //  Serial.print ("Receive: ") ;
    //  Serial.print (gReceivedFrameCount) ;
    //  Serial.print (" ") ;
    //  Serial.print (" STATUS 0x") ;
    //  Serial.print (TWAI_STATUS_REG, HEX) ;
    //  Serial.print (" RXERR ") ;
    //  Serial.print (TWAI_RX_ERR_CNT_REG) ;
    //  Serial.print (" TXERR ") ;
    //  Serial.println (TWAI_TX_ERR_CNT_REG) ;

    // Device ID
    frame.id = 0;
    frame.data[0] = 'M';
    frame.data[1] = 'a';
    frame.data[2] = 's';
    frame.data[3] = 't';
    frame.data[4] = 'e';
    frame.data[5] = 'r';
    frame.len = 6;

    bool ok = ACAN_ESP32::can.tryToSend(frame);
    if (ok)
    {
      gSentFrameCount += 1;
    }

    // Device ID
    frame.id = 1;
    frame.data64 = micros();
    frame.len = 8;

    ok = ACAN_ESP32::can.tryToSend(frame);
    if (ok)
    {
      gSentFrameCount += 1;
    }
  }

  while (ACAN_ESP32::can.receive(frame))
  {
    Serial.print("Frame : ");
    Serial.print(frame.id, DEC);

    Serial.print(" Message: ");

    if (frame.id == 100)
    {
      char buffer[10];
      sprintf(buffer, "%s", frame.data);
      Serial.println(buffer);
    }
    else
    {
      char buffer[10];
      sprintf(buffer, "%lu", frame.data64);
    }

    gReceivedFrameCount += 1;
  }

  delay(1);
}

void loop()
{
  webServer.loop();

  if (DeviceSettings::getInstance().getMQTTEnabled())
  {
    mqtt.loop();
  }

  if (DeviceSettings::getInstance().getCanbusEnabled())
  {
    handleCAN();
  }

  if (IOModule::getInstance().checkInputs())
  {
    handleInputChange();
  }

  handleInputDimming();

  handleTempMeasurement();

  StatusLed::update(); 
}

