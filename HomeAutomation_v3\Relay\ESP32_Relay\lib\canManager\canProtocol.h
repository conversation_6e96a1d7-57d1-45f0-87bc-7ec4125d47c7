#pragma once

namespace CANProtocol {
    // Command types
    enum Command {
        GET_INPUT_STATE = 0x01,
        SET_OUTPUT_STATE = 0x02,
        INPUT_STATE_RESPONSE = 0x03,
        OUTPUT_STATE_RESPONSE = 0x04,
        HEARTBEAT = 0x05
    };

    // Message structure (8 bytes)
    struct MessageFormat {
        // Byte 0: Command type
        // Byte 1: Port number (input/output)
        // Byte 2: Value (0/1 for state)
        // Bytes 3-7: Reserved for future use
    };

    // CAN ID format (11-bit identifier)
    // Bits 8-10: Message type (command/response)
    // Bits 0-7: Device ID (derived from MAC)
    
    // Helper functions
    inline uint16_t createCanId(uint8_t deviceId, Command cmd) {
        return ((uint16_t)(cmd & 0x07) << 8) | (deviceId & 0xFF);
    }

    inline uint8_t getDeviceIdFromCanId(uint16_t canId) {
        return canId & 0xFF;
    }

    inline Command getCommandFromCanId(uint16_t canId) {
        return static_cast<Command>((canId >> 8) & 0x07);
    }
}