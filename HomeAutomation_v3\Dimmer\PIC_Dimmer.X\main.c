#include "config.h"
#include "eeprom.h"
#include <xc.h>
#include <string.h>
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

#define PIN_INTERRUPT LATDbits.LATD7

#define START_BYTE  '!'
#define END_BYTE    ';'
#define OK_BYTE     '#'
#define ERROR_BYTE  '@'
#define ERROR_COMMAND  '^'
#define UART_BUFFER_SIZE 16

#define TMR1_ON T1CONbits.TMR1ON
#define TMR3_ON T3CONbits.TMR3ON

#define NUM_INPUTS 12
#define NUM_OUTPUTS 5

#define ZCD_OFFSET 0xFB30

#define MAX_VALUE 500

char APP_VERSION  = 1;

volatile char rxBuffer[UART_BUFFER_SIZE];
volatile char rxBufferSize = 0;
volatile char rxCommandReady = 0;

volatile unsigned int PWM_Count = 0;
volatile unsigned char PWM_Offset = 0;

//volatile int ZCD_Prediction_Count = 0;

//int chan_dir = 1;

typedef struct {
    uint8_t state;              // debounced value 
    uint8_t pinState;           //value of the PORT

    uint8_t debouncing;         //1 = debouncing in progress
    uint8_t debouncedState;     //1 = waiting for debounce
    volatile uint16_t debounceCounter;    //Counter for debouncing
} InputPin;

volatile InputPin inputsPins[12] = {
    {0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0},{0,0,0,0,0}
};

//Validated and debounced inputs
//volatile uint16_t inputsLastCheckedState;
volatile uint8_t pinsToDebounce = 0;

volatile uint16_t channelDesiredValue[NUM_OUTPUTS];   // Used for Fade in/out
volatile uint16_t channelValue[NUM_OUTPUTS];          // Actual channel value
volatile uint16_t channelType[NUM_OUTPUTS];           // 0 = Trailing , 1 = Leading edge

// Set the lower limit of the value. Below this threshold the light is off
//volatile uint16_t channelMinValue[5] = {0,0,0,0,0};
// Set the upper limit of the value. Above this threshold the light is on
//volatile uint16_t channelMaxValue[5] = {MAX_VALUE, MAX_VALUE, MAX_VALUE, MAX_VALUE, MAX_VALUE};       

//Time in ms
volatile uint16_t buttonDebounceTime = 10;
volatile uint16_t debounceCounter = 0;

//At 50hz we have a period every 20ms.
//Each half period is 10ms.
//That means, that we can change the dim value every 10ms
//to loop through the range of 1000 , we will need 1000 * 10ms = 10 seconds 
//turnOn/Off increment is the value we + or - every 10ms until we reach our value
// 1000/50 = 20 * 10ms = 200ms for fade effect
uint8_t fadeInIncrement = 5;
uint8_t fadeOutIncrement = 5;

volatile uint8_t fadeIsPending = 0;

void Config(void) {
    // Disable interrupts during configuration
    INTCONbits.GIE = 0;
    INTCONbits.PEIE = 0;

    //Xtal config @ 64MHz
    OSCCON1 = 0b01100000;    // NOSC bits set to HFINTOSC (0b110)
    OSCFRQ = 0x08;           // Set HFINTOSC to 64 MHz
    OSCEN = 0x00;            // All other oscillators disabled
    
    // Reset all PPS registers to default
    PPSLOCK = 0x55;
    PPSLOCK = 0xAA;
    PPSLOCKbits.PPSLOCKED = 0;
    
    // Disable unused interrupts
    PIE1 = 0;
    PIE2 = 0;
    PIE3 = 0;
    PIE4 = 0;
    PIE5 = 0;
    PIE6 = 0;
    PIE7 = 0;

     // Configure external interrupt
     INTCONbits.INT0EDG = 0;  // Interrupt on falling edge of INT0 pin
     PIE0bits.INT0IE = 1;     // External Interrupt Enable
     IPR0bits.INT0IP = 1;     // High priority
     
     // Disable unused modules to save power
     PMD0 = 0xFF;             // Disable all peripheral modules in PMD0
     PMD0bits.SYSCMD = 0;     // Enable system clock
     PMD0bits.NVMMD = 0;      // Enable NVM for EEPROM operations
     
     PMD1 = 0xFF;             // Disable all peripheral modules in PMD1
     PMD1bits.TMR1MD = 0;     // Enable Timer1
     
     PMD2 = 0xFF;             // Disable all analog modules
     
     PMD3 = 0xFF;             // Disable CLC, PWM, CCP
     
     PMD4 = 0xFF;             // Disable all peripheral modules in PMD4
     PMD4bits.UART1MD = 0;    // Enable UART1
     
     PMD5 = 0xFF;             // Disable CLC, DSM

    Config_IO();
    Config_UART();
    Config_Timer1();
    Config_Timer3();
    
    WDTCON0bits.SEN = 0; //Disable WDT

     // Lock PPS
     PPSLOCK = 0x55;
     PPSLOCK = 0xAA;
     PPSLOCKbits.PPSLOCKED = 1;
 
     // Clear all interrupt flags before enabling
     PIR0 = 0;
     PIR1 = 0;
     PIR2 = 0;
     PIR3 = 0;
     PIR4 = 0;
     PIR5 = 0;
     PIR6 = 0;
     PIR7 = 0;
     
     // Configure interrupt priorities
     INTCONbits.IPEN = 1;     // Enable interrupt priority
 
     // Enable interrupts
     INTCONbits.GIEL = 1;     // Enable low priority interrupts
     INTCONbits.GIEH = 1;     // Enable high priority interrupts
    
    //Time to settle
    __delay_ms(10);
}

void Config_IO(void) {
    
    // Disable all analog functions
    ANSELA = 0x00;
    ANSELB = 0x00;
    ANSELC = 0x00;
    ANSELD = 0x00;
    ANSELE = 0x00;
    
    // Disable weak pull-ups
    WPUA = 0x00;
    WPUB = 0x00;
    WPUC = 0x00;
    WPUD = 0x00;
    WPUE = 0x00;
    
    // Configure I/O directions
    TRISA = 0b00000000;
    TRISB = 0b00000001;
    TRISC = 0b10011111;
    TRISD = 0b01111111;
    TRISE = 0b000;
    
    // Disable open-drain
    ODCONA = 0x00;
    ODCONB = 0x00;
    ODCONC = 0x00;
    ODCOND = 0x00;
    ODCONE = 0x00;
    
    // Configure slew rate for outputs
    SLRCONA = 0x00;  // Maximum slew rate
    SLRCONB = 0x00;
    SLRCONC = 0x00;
    SLRCOND = 0x00;
    SLRCONE = 0x00;
    
    // Set initial output states to known values
    LATA = 0x00;
    LATB = 0x00;
    LATC = 0x00;
    LATD = 0x00;
    LATE = 0x00;
}

void Config_UART(void) {
    
    // Configure the Peripheral Pin Select (PPS)
    RC6PPS = 0x09;  // RC6 -> TX1 (Transmit)
    RX1PPS = 0x17; // RC7 -> RX1 (Receive)
    
    // Configure EUSART1
    TX1STAbits.SYNC = 0; // Asynchronous mode
    TX1STAbits.BRGH = 1; // High Baud Rate Select bit
    BAUD1CONbits.BRG16 = 1; // 8-bit Baud Rate Generator

    SP1BRGL = 138; // Set baud rate to 115200
    SP1BRGH = 0;   // High byte of SPBRG (only used if BRG16 = 1)

    RC1STAbits.SPEN = 1; // Enable EUSART1 (Serial Port Enable bit)
    TX1STAbits.TXEN = 1; // Enable transmitter
    RC1STAbits.CREN = 1; // Enable continuous receive

    // Configure the TX1 pin as an output
    TRISCbits.TRISC6 = 0; // RC6 as output (TX)
    TRISCbits.TRISC7 = 1; // RC7 as input (RX)
    
    //Enable interrupts
    PIE3bits.RC1IE = 1;
    IPR3bits.RC1IP = 1; //Hight priority
}

void Config_Timer1(void) {
    // Timer1 used to generate PWM for the mosfets
    // Internal clock (FOSC / 4)
    // Prescaler=1:1; TMR1 Preset=65456; Freq=100,00kHz; Period=10,00 µs
    
    // Configure Timer1
    TMR1CLK = 0b00001; // Fosc/4
    //TMR1CLK = 0b00010; // Fosc

    T1CONbits.CKPS = 0b00; // Prescaler 1:1
    T1GCON = 0;   // Disable Timer1 Gate function
    T1CONbits.RD16 = 1;
    
    TMR1 = 0xFF7A;         
    
    PIR4bits.TMR1IF = 0;    // Clear Timer1 interrupt flag
    PIE4bits.TMR1IE = 1;    // Enable Timer1 interrupt
    PIE5bits.TMR1GIE = 0;
    
    IPR4bits.TMR1IP = 1;    // Hight priority
    
    TMR1_ON = 1;   // Turn on Timer1
}

void Config_Timer3(void) {
    
    // Configure Timer3
    TMR3CLK = 0b00001; // Fosc/4
    T3CONbits.CKPS = 0b11; // Prescaler 1:8
    T3GCON = 0;   // Disable Timer3 Gate function
    T3CONbits.RD16 = 1;
    
    TMR3 = ZCD_OFFSET;         
    
    PIR4bits.TMR3IF = 0;    // Clear Timer3 interrupt flag
    PIE4bits.TMR3IE = 1;    // Enable Timer3 interrupt
    PIE5bits.TMR3GIE = 0;
    
    IPR4bits.TMR3IP = 1;    // Hight priority

    TMR3_ON = 0;
}


void Check_IO() {
    
    uint16_t inputs = readInputs();
    
    for(int i = 0; i < NUM_INPUTS; i++) {
        inputsPins[i].pinState = (inputs >> i) & 1;
        
        if(inputsPins[i].pinState != inputsPins[i].state && inputsPins[i].debouncing == 0) {
            //Debounce
            inputsPins[i].debouncing = 1;
            inputsPins[i].debouncedState = inputsPins[i].pinState;
            inputsPins[i].debounceCounter = 0;
            
            pinsToDebounce++;
            continue;
        }
        
        //Check for Debounce 
        if(inputsPins[i].debouncing == 1) {
            //Check for the same
            if(inputsPins[i].pinState != inputsPins[i].debouncedState) {
                //The input pin has changed, stop debouncing
                inputsPins[i].debouncing = 0;
                inputsPins[i].debounceCounter = 0;
            } else if(inputsPins[i].debounceCounter >= buttonDebounceTime) {
                //Set the pin to its new value
                inputsPins[i].debouncing = 0;
                inputsPins[i].debounceCounter = 0;
                inputsPins[i].state = inputsPins[i].debouncedState;
                
                //Notify the ESP32
                PIN_INTERRUPT = 1;
            }
        }
    }
}

void Check_UART_Errors() {
    
    // Clear any existing errors
    if (RC1STAbits.OERR) {
        RC1STAbits.CREN = 0;
        RC1STAbits.CREN = 1;
        RC1STAbits.SPEN = 0;
        RC1STAbits.SPEN = 1;
    }
    
    // Add error checking in the ISR
    if (RC1STAbits.FERR) {
        // Clear errors
        uint8_t dummy = RC1REG;  // Read RCREG to clear FERR
        RC1STAbits.CREN = 0;    // Clear OERR
        RC1STAbits.CREN = 1;
        UART_ClearBuffer();
        rxCommandReady = 0;
    }
}

void UART_ClearBuffer() {
    for(int i = 0; i < UART_BUFFER_SIZE; i++) {
        rxBuffer[i] = 0;
    }

    rxBufferSize = 0;
}

void UART_Write(char data) {
    while(!TX1STAbits.TRMT); // Wait until the transmit buffer is empty
    TX1REG = data;           // Transmit data
}

void UART_Write_String(const char *str) {
    while(*str != '\0') {
        UART_Write(*str);  // Send each character
        str++;             // Move to the next character
    }
}

void send_uint16_binary(uint16_t data) {
    char binary_string[17]; // 16 bits + null terminator
    binary_string[16] = '\0'; // Null terminator

    for (int i = 15; i >= 0; i--) {
        binary_string[i] = (data & 1) ? '1' : '0';
        data >>= 1;
    }

    // Send each character in the binary string
    for (int i = 0; i < 16; i++) {
        UART_Write(binary_string[i]);
    }
}

uint16_t readInputs(void) {
    
    uint16_t buffer = 0xFFFF;
    
    //buffer = PORTC << 8;
    buffer =  (uint16_t) (PORTC & 0b1111) << 8;
    buffer |= (uint16_t) PORTDbits.RD0 << 7;
    buffer |= (uint16_t) PORTDbits.RD1 << 6;
    buffer |= (uint16_t) PORTDbits.RD2 << 5;
    buffer |= (uint16_t) PORTDbits.RD3 << 4;
    buffer |= (uint16_t) PORTCbits.RC4 << 3;
    buffer |= (uint16_t) PORTDbits.RD4 << 2;
    buffer |= (uint16_t) PORTDbits.RD5 << 1;
    buffer |= (uint16_t) PORTDbits.RD6;
    
    //All inputs are pulled up
    //Invert all bits
    buffer = ~buffer;
    
    /*
        // Combine and shift the necessary bits from PORTC and PORTD directly
        buffer = (PORTC & 0x0F) << 8;  // Mask and shift lower 4 bits of PORTC
        buffer |= (PORTD & 0x7F) << 1; // Mask and shift lower 7 bits of PORTD

        // Extract bit RC4 and place it in the correct position
        buffer |= PORTCbits.RC4 << 3;  // Add RC4 to buffer
    */
    
    return buffer;
    
}

uint16_t readOutputs(void) {
    
    uint16_t buffer = 0;

    buffer |= (uint16_t) PORTBbits.RB5 << 8;
    buffer |= (uint16_t) PORTBbits.RB4 << 7;
    buffer |= (uint16_t) PORTBbits.RB3 << 6;
    buffer |= (uint16_t) PORTBbits.RB2 << 5;
    buffer |= (uint16_t) PORTBbits.RB1 << 4;
    buffer |= (uint16_t) PORTBbits.RB0 << 3;
    buffer |= (uint16_t) PORTAbits.RA2 << 2;
    buffer |= (uint16_t) PORTAbits.RA1 << 1;
    buffer |= (uint16_t) PORTAbits.RA0;
    
    return buffer;
}

void writeOutput(uint8_t id, uint8_t state) {
    switch (id) {
        case 0:
            LATAbits.LATA2 = state;
            break;
        case 1:
            LATAbits.LATA1 = state;
            break;
        case 2:
            LATAbits.LATA0 = state;
            break;
        case 3:
            LATBbits.LATB0 = state;
            break;
        case 4:
            LATBbits.LATB1 = state;
            break;
        case 5:
            LATBbits.LATB2 = state;
            break;
        case 6:
            LATBbits.LATB3 = state;
            break;
        case 7:
            LATBbits.LATB4 = state;
            break;
        case 8:
            LATBbits.LATB5 = state;
            break;
        default:
            break;
    }
}

void parseRxCommand() {
    if (rxBuffer[0] == 'R') {
        //Read all inputs
        //Send 2 bytes for inputs state
        
        uint16_t inputsLastCheckedState = 0x00;

        for(int i = 0; i < NUM_INPUTS; i++) {
            if(inputsPins[i].state == 1) {
                inputsLastCheckedState |= (1 << i);
            }
        }

        PIN_INTERRUPT = 0;

        // Split the 12 bits
        uint8_t highByte = (inputsLastCheckedState >> 6) << 2;  // Extract the upper 6 bits, add 0b00 at LSBs
        uint8_t lowByte = inputsLastCheckedState << 2;  		// Extract the lower 6 bits, add 0b00 at LSBs

        UART_Write(OK_BYTE);
        UART_Write(START_BYTE);
        UART_Write(highByte);
        UART_Write(lowByte);
        UART_Write(END_BYTE);
    } else if (rxBuffer[0] == 'O') {
        //Send all outputs values
        UART_Write(OK_BYTE);
        for(int i = 0; i < NUM_OUTPUTS; i++ ) {
            uint16_t value = channelValue[i];
            uint8_t highByte = (value >> 8);
            uint8_t lowByte = (value & 0xFF);
        
            UART_Write(highByte);
            UART_Write(lowByte);
        }
    } else if (rxBuffer[0] == 'W') {
        //Write output
        //Byte 1 is the port number 0 - 4 
        //Byte 2 is the port state (0 or 1)
        //Send OK

        if(rxBuffer[1] < 5) {
            //Output state
            if(rxBuffer[2] > 0) {
                channelDesiredValue[rxBuffer[1]] = MAX_VALUE;
            } else {
                channelDesiredValue[rxBuffer[1]] = 0;
            }
            
            fadeIsPending = 1;

            UART_Write(OK_BYTE);
        } else {
            UART_Write(ERROR_BYTE);
        }
    } else if (rxBuffer[0] == 'T') {
        //Set the output type (Trailing or Leading edge dimming)
        //Byte 1 is either 1 or 0
        //Send OK
        if(rxBuffer[1] < 5  && rxBuffer[2] <= 1) {
            channelType[rxBuffer[1]] = rxBuffer[2];    
            UART_Write(OK_BYTE);
        } else {
            UART_Write(ERROR_BYTE);
        }
    } else if (rxBuffer[0] == 'D') {
        //Byte 1 is port number 0 - 4
        //Byte 2,3 is dim rate 0 - 1000
        
        //value is shifted 1 bit so it don't collide with START BYTE and END BYTE
        uint16_t value = (((uint16_t)rxBuffer[2] << 8) | rxBuffer[3]) >> 1;
        
        if(rxBuffer[1] < NUM_OUTPUTS  && value <= MAX_VALUE) {
            
            channelDesiredValue[rxBuffer[1]] = value;
            fadeIsPending = 1;
            
            //Send OK
            UART_Write(OK_BYTE);
        } else {
            UART_Write(ERROR_BYTE);
        }
    } else if (rxBuffer[0] == '1') {
        //Byte 1,2 is Fade in rate 0 - 1000
        
        //value is shifted 1 bit so it don't collide with START BYTE and END BYTE
        //uint16_t value = (((uint16_t)rxBuffer[1] << 8) | rxBuffer[2]) >> 1;
        
        fadeInIncrement = rxBuffer[1];
            
        //Send OK
        UART_Write(OK_BYTE);
    } else if (rxBuffer[0] == '2') {
        //Byte 1,2 is Fade out rate 0 - 1000
        
        //value is shifted 1 bit so it don't collide with START BYTE and END BYTE
        //uint16_t value = (((uint16_t)rxBuffer[1] << 8) | rxBuffer[2]) >> 1;
        
        fadeOutIncrement = rxBuffer[1];
        
        //Send OK
        UART_Write(OK_BYTE);
    } else if (rxBuffer[0] == '3') {
        //Byte 1,2 is Debounce time (0 - 1000ms)
        
        //value is shifted 1 bit so it don't collide with START BYTE and END BYTE
        uint16_t value = (((uint16_t)rxBuffer[1] << 8) | rxBuffer[2]) >> 1;
        
        buttonDebounceTime = value;
        
        //Send OK
        UART_Write(OK_BYTE);
    } else if (rxBuffer[0] == 'V') {
        //Version
        UART_Write(OK_BYTE);
        UART_Write(APP_VERSION);
    } else if (rxBuffer[0] == 'X') {
        //Debug info
        
        
        char buffer[250];  // Enough space for a 3-digit number (0-255) and null terminator
        
        sprintf(buffer, "Debounce: %u\n", buttonDebounceTime);
        UART_Write_String(buffer);
        
        sprintf(buffer, "fadeInIncrement: %u\n", fadeInIncrement);
        UART_Write_String(buffer);
        
        sprintf(buffer, "fadeOutIncrement: %u\n", fadeOutIncrement);
        UART_Write_String(buffer);
        
        for(int i= 0; i < 5; i++) {
            sprintf(buffer, "channelType[%u] = %u\n", i, channelType[i]);
            UART_Write_String(buffer);
        }
        
        UART_Write_String("Inputs: ");
        
        uint16_t allInputs = readInputs();
        
        for (int i = 15; i >= 0; i--) {
            if(allInputs & (1 << i)) {
                UART_Write_String("1");
            } else {   
                UART_Write_String("0");
            }
        }
        
        UART_Write_String("\n");
        UART_Write(OK_BYTE);
        
    } else if (rxBuffer[0] == 'Q') {
        UART_Write(OK_BYTE);
        __delay_ms(10);
        __asm("reset");
    } else {
        //Unknown command, return error
        UART_Write(ERROR_COMMAND);
    }

    rxCommandReady = 0;
    UART_ClearBuffer();
}


void main(void) {

    Config();

    //Set default values
    for(int i = 0; i < NUM_OUTPUTS; i++) {
        channelValue[i] = 0;
        channelType[i] = 0;
    }
    
    
    while(1) {
        
        Check_IO();
        Check_UART_Errors();
        
        if(rxCommandReady == 1) {
            parseRxCommand();
        }
        __delay_us(10);
        
        //chan_value = 978; is max
        //chan_value = 978 / 2;
        //__delay_ms(50);
        //continue;
        /*
        if (chan_dir == 1) {
            channelValue[0]+=10;
            if (channelValue[0] > MAX_VALUE) {
                channelValue[0] = MAX_VALUE;
                chan_dir = 0;
                //__delay_ms(1000);
            }
        } else {
            channelValue[0]-=10;
            if (channelValue[0] <= 0) {
                channelValue[0] = 0;
                chan_dir = 1;
                //__delay_ms(1000);
            }
        }
        */
    }

    return;
}

void timer1Reset() {
    TMR1_ON = 0;
    PIR4bits.TMR1IF = 0;
    TMR1 = 0xFEE0; //Fosc/4
    TMR1_ON = 1;
}

void ZeroCrossDetected(void) {
    TMR3 = ZCD_OFFSET; 
    PIR4bits.TMR3IF = 0;
    TMR3_ON = 1;
}

void FadeEffect(void) {
    if(fadeIsPending > 0) {
        fadeIsPending = 0;
        uint16_t diff = 0;
        
        for(int i = 0; i < NUM_OUTPUTS; i++) {
            //Check for fade in (turn on)
            if(channelValue[i] < channelDesiredValue[i])  {
                if(fadeInIncrement > 0) {
                    diff = channelDesiredValue[i] - channelValue[i];
                    channelValue[i] += (diff < fadeInIncrement) ? diff : fadeInIncrement;
                    fadeIsPending = 1;
                } else {
                    channelValue[i] = channelDesiredValue[i];
                }
            } else if(channelValue[i] > channelDesiredValue[i]) {
                //Fade out (turn off)
                if(fadeOutIncrement > 0) {
                    diff = channelValue[i] - channelDesiredValue[i];
                    channelValue[i] -= (diff < fadeOutIncrement) ? diff : fadeOutIncrement;
                    fadeIsPending = 1;
                } else {
                    channelValue[i] = channelDesiredValue[i];
                }
            }
        }
    }
}

// High-priority ISR
void __interrupt(high_priority) ISR_HIGH(void) {
    
    //On pin change
    if (PIR0bits.INT0IF) {
        //Zero cross detected
        ZeroCrossDetected();
        
        //ZeroCross happens every 10ms
        //Perfect for the fade effect
        FadeEffect();
        
        PIR0bits.INT0IF = 0;
    }
    
    //Timer1 overflow
    if (PIR4bits.TMR1IF) {
        //First clear the flag, because the commands bellow add up to the timer delay
        timer1Reset();
        
        PWM_Count++;
        
        LATBbits.LATB1 = (channelType[0] == 0) ? channelValue[0] > PWM_Count : (MAX_VALUE - channelValue[0]) < PWM_Count;
        LATBbits.LATB2 = (channelType[1] == 0) ? channelValue[1] > PWM_Count : (MAX_VALUE - channelValue[1]) < PWM_Count;
        LATBbits.LATB3 = (channelType[2] == 0) ? channelValue[2] > PWM_Count : (MAX_VALUE - channelValue[2]) < PWM_Count;
        LATBbits.LATB4 = (channelType[3] == 0) ? channelValue[3] > PWM_Count : (MAX_VALUE - channelValue[3]) < PWM_Count;
        LATBbits.LATB5 = (channelType[4] == 0) ? channelValue[4] > PWM_Count : (MAX_VALUE - channelValue[4]) < PWM_Count;
        
        //PIN_INTERRUPT = (channelType[0] == 0) ? channelValue[0] > PWM_Count : (MAX_VALUE - channelValue[0]) < PWM_Count;

        if(pinsToDebounce > 0) {
            debounceCounter++;
            if(debounceCounter >= 50) {
                debounceCounter = 0;
				pinsToDebounce = 0;
                for(int i = 0;i < NUM_INPUTS;i++) {
                    if(inputsPins[i].debouncing == 1) {
                        inputsPins[i].debounceCounter++;
                        pinsToDebounce++;
                    }
                }
            }
        }
        
        /*
        for(int i = 0; i < 5; i++) {
            if(channelValue[i] == MAX_VALUE || ( channelType[i] == 0 && channelValue[i] > PWM_Count ) || ( channelType[i] == 1 && channelValue[i] < PWM_Count) ) {
                LATB |= (1 << (i + 1)); // Set the bit 
            } else {
                LATB &= ~(1 << (i + 1)); // Clear the bit 
            }
        }
        */

        if (PWM_Count >= MAX_VALUE) {
            //ZCD not found
            PWM_Count = 0;
        }
    }
    
    if (PIR4bits.TMR3IF) {
        //The real ZCD
        timer1Reset();
        PWM_Count = 0;
        
        TMR3_ON = 0;
        PIR4bits.TMR3IF = 0;
    }
    
    // UART receive interrupt
    if (PIR3bits.RC1IF) {
        uint8_t rxByte = RCREG;

        if(rxByte == START_BYTE) {
            UART_ClearBuffer();
        } else if(rxByte == END_BYTE) {
            rxCommandReady = 1;
        } else {
            rxBuffer[rxBufferSize++] = rxByte;
        }

        PIR3bits.RC1IF = 0;   
    }
    

    //Clear all other interrupts that might occure
    PIR0 = 0;
    PIR1 = 0;
    PIR2 = 0;
    PIR3 = 0;
    PIR4 = 0;
    PIR5 = 0;
    PIR6 = 0;
    PIR7 = 0;
    
}

// Low-priority ISR
void __interrupt(low_priority) ISR_LOW(void) {
}