#include <Arduino.h>
#include <WiFi.h>
#include <Wire.h>
#include <ACAN_ESP32.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <ArduinoHA.h>
#include "global_config.h"
#include "eventManager.h"
#include "deviceSettings.h"
#include "WebServer.h"
#include "ioModule.h"
#include "statusLed.h"
#include "canManager.h"



EthernetClient mqqtClient;

WebServer webServer(80);

HADevice HA_device;
HAMqtt mqtt(mqqtClient, HA_device, 40);

char *mqtt_username;
char *mqtt_password;
bool mqtt_connected = false;

HABinarySensor haSensorInput1("Input1");
HABinarySensor haSensorInput2("Input2");
HABinarySensor haSensorInput3("Input3");
HABinarySensor haSensorInput4("Input4");
HABinarySensor haSensorInput5("Input5");
HABinarySensor haSensorInput6("Input6");
HABinarySensor haSensorInput7("Input7");
HABinarySensor haSensorInput8("Input8");
HABinarySensor haSensorInput9("Input9");
HABinarySensor haSensorInput10("Input10");
HABinarySensor haSensorInput11("Input11");
HABinarySensor haSensorInput12("Input12");

HALight haLightOutput1("Output1");
HALight haLightOutput2("Output2");
HALight haLightOutput3("Output3");
HALight haLightOutput4("Output4");
HALight haLightOutput5("Output5");
HALight haLightOutput6("Output6");
HALight haLightOutput7("Output7");
HALight haLightOutput8("Output8");
HALight haLightOutput9("Output9");

HASensorNumber haEnergyOutput1("Power1", HABaseDeviceType::PrecisionP3);
HASensorNumber haEnergyOutput2("Power2", HABaseDeviceType::PrecisionP3);
HASensorNumber haEnergyOutput3("Power3", HABaseDeviceType::PrecisionP3);
HASensorNumber haEnergyOutput4("Power4", HABaseDeviceType::PrecisionP3);
HASensorNumber haEnergyOutput5("Power5", HABaseDeviceType::PrecisionP3);
HASensorNumber haEnergyOutput6("Power6", HABaseDeviceType::PrecisionP3);
HASensorNumber haEnergyOutput7("Power7", HABaseDeviceType::PrecisionP3);
HASensorNumber haEnergyOutput8("Power8", HABaseDeviceType::PrecisionP3);
HASensorNumber haEnergyOutput9("Power9", HABaseDeviceType::PrecisionP3);

HASensorNumber haTemperatureSensor("Temperature", HASensorNumber::PrecisionP2);

HABinarySensor* haBynaryDevices[] = {&haSensorInput1, &haSensorInput2, &haSensorInput3, &haSensorInput4, &haSensorInput5, &haSensorInput6, &haSensorInput7, &haSensorInput8, &haSensorInput9, &haSensorInput10, &haSensorInput11, &haSensorInput12};
HALight*        haLightDevices[] = {&haLightOutput1, &haLightOutput2, &haLightOutput3, &haLightOutput4, &haLightOutput5, &haLightOutput6, &haLightOutput7, &haLightOutput8, &haLightOutput9};
HASensorNumber* haEnergyDevices[] = {&haEnergyOutput1, &haEnergyOutput2, &haEnergyOutput3, &haEnergyOutput4, &haEnergyOutput5, &haEnergyOutput6, &haEnergyOutput7, &haEnergyOutput8, &haEnergyOutput9};

OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature tempSensor(&oneWire);


// CANbus
static uint32_t gBlinkLedDate = 0;
static uint8_t gReceivedFrameCount = 0;
static uint32_t gSentFrameCount = 0;

//const int temperatureUpdateTimeout = 30000; //in ms
//const int temperatureUpdateReadyTimeout =  temperatureUpdateTimeout + 750; //in ms
//unsigned long previousTemperatureUpdate = 0;
//bool isTempConversionInProgress = false;

//Update hold buttons
const int buttonHoldUpdateTimeout = 100; //in ms
unsigned long previousButtonHoldUpdate = 0;
uint8_t buttonHoldEnable = 0;

//Tasks
TaskHandle_t tempTaskHandle = NULL;
TaskHandle_t inputsTaskHandle = NULL;
TaskHandle_t powerReportTaskHandle = NULL;

// Temperature reading task
void TempTask(void *pvParameters) {
  while (1) {
    tempSensor.requestTemperatures();  // Start conversion (takes ~750ms)

      // Continue doing other things while waiting
      vTaskDelay(pdMS_TO_TICKS(750));  // Wait for the conversion (750ms)

      // Read temperature
      WebServer::temperature = tempSensor.getTempCByIndex(0);

      #ifdef DEBUG_TO_UART
        Serial.println("Temperature: " + String(WebServer::temperature) + " °C");
      #endif

      if(WebServer::temperature >= MAX_TEMPERATURE) {
        //Emergency shutdown
        for(int i = 0; i < NUM_OUTPUTS; i++) {
          IOModule::getInstance().setOutput(i, 0);
  
          if (DeviceSettings::getInstance().getMQTTEnabled()) {
            haLightDevices[i]->setBrightness(0, true);
          }
        }
  
        if(DeviceSettings::getInstance().getEventLogEnabled()) {
          EventManager::getInstance().logEvent(EventManager::EventType::OVERHEAT, (uint8_t) (WebServer::temperature));
        }
      }
  
      if (DeviceSettings::getInstance().getMQTTEnabled())
      {
        haTemperatureSensor.setValue(WebServer::temperature);
      }
  

      vTaskDelay(pdMS_TO_TICKS(10000));  // Wait before next reading
  }
}

// Inputs reading task
void InputsTask(void *pvParameters) {
  while (1) {
    if (IOModule::getInstance().checkInputs())
    {
      uint16_t oldState = IOModule::getInstance().oldInputPorts;
      uint16_t newState = IOModule::getInstance().inputPorts;

      bool isMqttEnabled = DeviceSettings::getInstance().getMQTTEnabled();
      bool isEnergyMonitoringEnabled = DeviceSettings::getInstance().isEnergyMonitoringEnabled();
      //IOModule::getInstance().debug();

      for (int i = 0; i < NUM_INPUTS; i++)
      {
        // Extract the bit at position i for both variables
        uint8_t oldBit = (oldState >> i) & 0x01;
        uint8_t newBit = (newState >> i) & 0x01;

        // Compare the bits
        if (oldBit == newBit)
        {
            //No change
            continue;
        }

  #ifdef DEBUG_TO_UART
        Serial.print("Change on input: ");
        Serial.println(i);
  #endif

        EventManager::getInstance().logEvent(EventManager::INPUT_CHANGE, i, newBit);

        DeviceSettings::PortEnum portType = DeviceSettings::getInstance().getPortType(i);
        int portMap = DeviceSettings::getInstance().getPortMap(i);
        
        switch (portType)
        {
          case DeviceSettings::PortEnum::SWITCH:                  //On / Off
            IOModule::getInstance().setOutput(portMap, newBit);
            if(isMqttEnabled) {
                haLightDevices[portMap]->setState(newBit > 0);
                if(isEnergyMonitoringEnabled) {
                  float power = (newBit > 0) ? DeviceSettings::getInstance().getOutputEnergyConsumption(portMap) : 0.0f;
                  haEnergyDevices[portMap]->setValue(power, true);
                }
            }
            break;
          case DeviceSettings::PortEnum::INVERTED_SWITCH:         //Off / On
            IOModule::getInstance().setOutput(portMap, !newBit);
            if(isMqttEnabled) {
                haLightDevices[portMap]->setState(newBit == 0);
                if(isEnergyMonitoringEnabled) {
                  float power = (newBit == 0) ?DeviceSettings::getInstance().getOutputEnergyConsumption(portMap) : 0.0f;
                  haEnergyDevices[portMap]->setValue(power, true);
                }
            }
            break;
          case DeviceSettings::PortEnum::TOGGLE:
            IOModule::getInstance().toggleOutput(portMap);
            if(isMqttEnabled) {
              haLightDevices[portMap]->setState(!haLightDevices[portMap]->getCurrentState());
              if(isEnergyMonitoringEnabled) {
                float power = (!haLightDevices[portMap]->getCurrentState()) ? DeviceSettings::getInstance().getOutputEnergyConsumption(portMap) : 0.0f;
                haEnergyDevices[portMap]->setValue(power, true);
              }
            }
            break;
          case DeviceSettings::PortEnum::HA_BUTTON : //Do nothing to the outputs just report to HA
          default:
            break;
        }

        if(isMqttEnabled) {
          haBynaryDevices[i]->setState(newBit, true);
        }
        
      }
    }
 
    vTaskDelay(pdMS_TO_TICKS(20));  // Wait 20ms before next reading. Default debounce is 100ms.
  }
}

void PowerReportTask(void* parameter) {
    while (1) {
        if(DeviceSettings::getInstance().isEnergyMonitoringEnabled()) {
            for(int i = 0; i < NUM_OUTPUTS; i++) {
                // Check if output is ON
                bool outputState = (IOModule::getInstance().outputPorts >> i) & 0x01;
                if(outputState) {
                    // Calculate energy consumption for the last minute
                    float powerWatts = DeviceSettings::getInstance().getOutputEnergyConsumption(i);

                    if(powerWatts <= 0.0f) {
                        continue;
                    }

                    // Convert Watts to Watt-hours for the 1-minute interval
                    float energyWh = powerWatts / 60.0f;  // Convert 1 minute of usage to Wh
                    
                    // Get current value and add new consumption
                    float currentValue = haEnergyDevices[i]->getCurrentValue().toFloat();
                    float newValue = currentValue + energyWh;
                    
                    haEnergyDevices[i]->setValue(newValue, true);
                }
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(60000));
    }
}

void onMqttStateChanged(HAMqtt::ConnectionState state) {
    int8_t int_state = static_cast<int8_t>(state);

    switch (int_state)
    {
      case -5:
        Serial.println(F("MQTT state changed to: StateConnecting"));
        mqtt_connected = false;
        break;
      case -4:
        Serial.println(F("MQTT state changed to: StateConnectionTimeout"));
        mqtt_connected = false;
        break;
      case -3:
        Serial.println(F("MQTT state changed to: StateConnectionLost"));
        mqtt_connected = false;
        break;
      case -2:
        Serial.println(F("MQTT state changed to: StateConnectionFailed"));
        mqtt_connected = false;
        break;
      case -1:
        Serial.println(F("MQTT state changed to: StateDisconnected"));
        mqtt_connected = false;
        break;
      case 0:
        Serial.println(F("MQTT state changed to: StateConnected"));
        mqtt_connected = true;
        break;
      case 1:
        Serial.println(F("MQTT state changed to: StateBadProtocol"));
        mqtt_connected = false;
        break;
      case 2:
        Serial.println(F("MQTT state changed to: StateBadClientId"));
        mqtt_connected = false;
        break;
      case 3:
        Serial.println(F("MQTT state changed to: StateUnavailable"));
        mqtt_connected = false;
        break;
      case 4:
        Serial.println(F("MQTT state changed to: StateBadCredentials"));
        mqtt_connected = false;
        break;
      case 5:
        Serial.println(F("MQTT state changed to: StateUnauthorized"));
        mqtt_connected = false;
        break;
      default:
        break;
    }

    Serial.println(static_cast<int8_t>(state));
}

void onStateCommand(bool state, HALight* sender) {
    Serial.print("State: ");
    Serial.println(state);

    for(int i = 0; i < NUM_OUTPUTS; i++) {
        if(haLightDevices[i] == sender) {
            IOModule::getInstance().setOutput(i, (state == true?1:0));
            EventManager::getInstance().logEvent(EventManager::EventType::MQTT_STATE, i, (state == true)? 1 : 0);
            break;
        }
    }

    sender->setState(state); // report state back to the Home Assistant
}


void initHADevices() {

#ifdef DEBUG_TO_UART
  Serial.println("INIT - HA Devices");
#endif

  const byte *deviceId = DeviceSettings::getInstance().getMQTTDeviceID();

  HA_device.setUniqueId(deviceId, 6);
  HA_device.setSoftwareVersion(TOSTRING(APP_SOFTWARE_VERSION));
  HA_device.setName("Lights - 9ch");
  HA_device.setManufacturer("IceSoft");
  HA_device.setModel("DIN - 9ch triac");
  HA_device.setConfigurationUrl(webServer.localURL);

  // This method enables availability for all device types registered on the device.
  // For example, if you have 5 sensors on the same device, you can enable
  // shared availability and change availability state of all sensors using
  // single method call "device.setAvailability(false|true)"
  //HA_device.enableSharedAvailability();

  // Optionally, you can enable MQTT LWT feature. If device will lose connection
  // to the broker, all device types related to it will be marked as offline in
  // the Home Assistant Panel.
  HA_device.enableLastWill();

  // The unique ID of each device type will be prefixed with the device's ID once enabled.
  HA_device.enableExtendedUniqueIds();

  
  uint16_t inputState = IOModule::getInstance().inputPorts;


  for(int i = 0; i < NUM_INPUTS; i++) {
    //in order to set custom name
    //you must use global variable, because thats the way MQTT lib is referenceing them
    //check the user and password copy below
    haBynaryDevices[i]->setName(haBynaryDevices[i]->uniqueId());
    haBynaryDevices[i]->setIcon("mdi:import");
    haBynaryDevices[i]->setCurrentState( (inputState >> i) & 0x01);
  }

  for(int i = 0; i < NUM_OUTPUTS; i++) {
    haLightDevices[i]->setName(haLightDevices[i]->uniqueId());
    haLightDevices[i]->onStateCommand(onStateCommand);
    
    // Get actual output state from IOModule
    bool outputState = (IOModule::getInstance().outputPorts >> i) & 0x01;
    haLightDevices[i]->setCurrentState(outputState);

    haEnergyDevices[i]->setName(haEnergyDevices[i]->uniqueId());
    haEnergyDevices[i]->setUnitOfMeasurement("Wh");
    haEnergyDevices[i]->setDeviceClass("energy");
    haEnergyDevices[i]->setStateClass("total_increasing");
    haEnergyDevices[i]->setCurrentValue(0.0f);  // Start from 0
  }

  haTemperatureSensor.setIcon("mdi:temperature-celsius");
  haTemperatureSensor.setName("Temperature");
  haTemperatureSensor.setUnitOfMeasurement("C");
  
  mqtt.onStateChanged(onMqttStateChanged);
  
  //Copy the username to a global variable. 
  //The mqtt lib will not work otherway     
  int strLen = DeviceSettings::getInstance().getMQTTUsername().length() + 1; // +1 for the null terminator
  mqtt_username = new char[strLen];
  DeviceSettings::getInstance().getMQTTUsername().toCharArray(mqtt_username, strLen);
  
  strLen = DeviceSettings::getInstance().getMQTTPassword().length() + 1; // +1 for the null terminator
  mqtt_password = new char[strLen];
  DeviceSettings::getInstance().getMQTTPassword().toCharArray(mqtt_password, strLen);

  mqtt.begin(DeviceSettings::getInstance().getMQTTIPAddress(), mqtt_username, mqtt_password);
}

void initCanbus() {
#ifdef DEBUG_TO_UART
    Serial.println("INIT - Can Bus");
#endif

    if (!CANManager::getInstance().begin(CAN_RX_PIN, CAN_TX_PIN)) {
        Serial.println("CAN initialization failed!");
    }
}

void setup()
{
  // Init pins
  pinMode(LAN_RESET, OUTPUT);
  pinMode(LAN_INTERRUPT, INPUT);
  pinMode(STATUS_LED, OUTPUT);
  pinMode(PIC_INTERRUPT_PIN, INPUT);
  pinMode(PIC_MCLR, INPUT);
  pinMode(PIC_PGD, INPUT);
  pinMode(PIC_PGC, INPUT);

  digitalWrite(LAN_RESET, LOW);
  
  StatusLed::begin(STATUS_LED); 

  Serial.begin(115200);

  // Disable WiFi
  WiFi.disconnect(true);
  WiFi.mode(WIFI_OFF);

  DeviceSettings::getInstance().begin();
  //DeviceSettings::getInstance().resetPreferences();

  // Initialize pic IO module
  IOModule::getInstance().begin(PIC_RX_PIN, PIC_TX_PIN, PIC_INTERRUPT_PIN);
  IOModule::getInstance().setDefaults();
  IOModule::getInstance().readInputs();
  IOModule::getInstance().readOutputs();
    

  tempSensor.begin();

  // Wait
  delay(50);

  // Start LAN
  digitalWrite(LAN_RESET, HIGH);

  webServer.begin();

  // Init CAN
  if (DeviceSettings::getInstance().getCanbusEnabled())
  {
    initCanbus();
  }

  // Init HA Devices
  if (DeviceSettings::getInstance().getMQTTEnabled())
  {
    initHADevices();
  }

  DeviceAddress tempDeviceAddress;
  tempSensor.getAddress(tempDeviceAddress, 0);
  tempSensor.setResolution(tempDeviceAddress, 10);

  // Create temperature task
  xTaskCreatePinnedToCore(TempTask, "TempTask", 2048, NULL, 1, &tempTaskHandle, 1);  // Run on Core 1 with priotity 1 (lowest)
  xTaskCreatePinnedToCore(InputsTask, "InputsTask", 4096, NULL, 10, &inputsTaskHandle, 1);  // Run on Core 1 with priority 10 (the highest is 24)

  // Create power report task
  if(DeviceSettings::getInstance().isEnergyMonitoringEnabled()) {
    xTaskCreatePinnedToCore(
        PowerReportTask,
        "PowerReport",
        2048,
        NULL,
        1,  // Low priority
        &powerReportTaskHandle,
        1   // Run on Core 1
    );
  }
  
  if(DeviceSettings::getInstance().getEventLogEnabled()) {
    EventManager::getInstance().begin();
  }

}

void handleCAN() {
    static uint32_t lastHeartbeat = 0;
    const uint32_t HEARTBEAT_INTERVAL = 1500; // 1.5 seconds

    // Send periodic heartbeat
    if (millis() - lastHeartbeat >= HEARTBEAT_INTERVAL) {
        CANManager::getInstance().sendHeartbeat();
        lastHeartbeat = millis();
    }

    // Process incoming messages
    CANManager::getInstance().update();
}

void loop()
{
  webServer.loop();

  if (DeviceSettings::getInstance().getMQTTEnabled())
  {
    mqtt.loop();
  }

  if (DeviceSettings::getInstance().getCanbusEnabled())
  {
    handleCAN();
  }

  StatusLed::update(); 

  delay(1);
}
