//
// !!! WARNING !!! AUTO-GENERATED FILE!
// PLEASE DO NOT MODIFY IT AND USE "platformio.ini":
// https://docs.platformio.org/page/projectconf/section_env_build.html#build-flags
//
{
    "configurations": [
        {
            "name": "PlatformIO",
            "includePath": [
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/Atmel_EnergyMeter/include",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/Atmel_EnergyMeter/src",
                "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/Atmel_EnergyMeter/lib/emonLibDB",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/cores/dxcore/api/deprecated",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/cores/dxcore",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/variants/48pin-standard",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/AzduinoMAX38903/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Comparator/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/DxCore/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/EEPROM/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Event/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Flash/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Logic/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Opamp/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/SD/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/SPI/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Servo/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Servo_DxCore/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/SoftwareSerial/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/USERSIG/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Wire/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/ZCD/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/tinyNeoPixel",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/tinyNeoPixel_Static",
                ""
            ],
            "browse": {
                "limitSymbolsToIncludedHeaders": true,
                "path": [
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/Atmel_EnergyMeter/include",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/Atmel_EnergyMeter/src",
                    "C:/Users/<USER>/Desktop/My Stuff/Home GIT/Projects/HomeAutomation_v3/EnergyMeter/Atmel_EnergyMeter/lib/emonLibDB",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/cores/dxcore/api/deprecated",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/cores/dxcore",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/variants/48pin-standard",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/AzduinoMAX38903/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Comparator/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/DxCore/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/EEPROM/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Event/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Flash/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Logic/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Opamp/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/SD/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/SPI/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Servo/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Servo_DxCore/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/SoftwareSerial/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/USERSIG/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/Wire/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/ZCD/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/tinyNeoPixel",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-megaavr-dxcore/libraries/tinyNeoPixel_Static",
                    ""
                ]
            },
            "defines": [
                "PLATFORMIO=60118",
                "ARDUINO_AVR_AVR128DB48",
                "ARDUINO_avrdb",
                "F_CPU=24000000L",
                "ARDUINO_ARCH_MEGAAVR",
                "ARDUINO=10808",
                "CLOCK_SOURCE=0",
                "DXCORE=\"1.5.6\"",
                "DXCORE_MAJOR=1UL",
                "DXCORE_MINOR=5UL",
                "DXCORE_PATCH=6UL",
                "DXCORE_RELEASED=1",
                "CORE_ATTACH_ALL",
                "TWI_MORS_SINGLE",
                "MILLIS_USE_TIMERB2",
                ""
            ],
            "cStandard": "gnu11",
            "cppStandard": "gnu++17",
            "compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-atmelavr/bin/avr-gcc.exe",
            "compilerArgs": [
                "-mmcu=avr128db48",
                "-mrelax",
                ""
            ]
        }
    ],
    "version": 4
}
