<?xml version="1.0" encoding="UTF-8"?>
<configurationDescriptor version="65">
  <projectmakefile>Makefile</projectmakefile>
  <defaultConf>0</defaultConf>
  <confs>
    <conf name="default" type="2">
      <platformToolSN>:=MPLABComm-USB-Microchip:=&lt;vid>04D8:=&lt;pid>900A:=&lt;rev>0002:=&lt;man>Microchip Technology Inc.:=&lt;prod>PICkit 3:=&lt;sn>BUR131088788:=&lt;drv>x:=&lt;xpt>h:=end</platformToolSN>
      <languageToolchainDir>C:\Program Files\Microchip\xc8\v2.50\bin</languageToolchainDir>
      <mdbdebugger version="1">
        <placeholder1>place holder 1</placeholder1>
        <placeholder2>place holder 2</placeholder2>
      </mdbdebugger>
      <runprofile version="6">
        <args></args>
        <rundir></rundir>
        <buildfirst>true</buildfirst>
        <console-type>0</console-type>
        <terminal-type>0</terminal-type>
        <remove-instrumentation>0</remove-instrumentation>
        <environment>
        </environment>
      </runprofile>
    </conf>
  </confs>
</configurationDescriptor>
