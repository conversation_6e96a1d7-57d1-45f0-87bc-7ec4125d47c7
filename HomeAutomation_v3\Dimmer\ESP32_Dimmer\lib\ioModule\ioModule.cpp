#include "ioModule.h"

IOModule::IOModule() {

}

IOModule& IOModule::getInstance() {
  static IOModule instance;
  return instance;
}

void IOModule::begin(uint8_t RX_PIN, uint8_t TX_PIN, uint8_t INTERRUPT_PIN)
{
    Serial2.setPins(RX_PIN, TX_PIN);
    Serial2.begin(115200);

    _interrupt_pin = INTERRUPT_PIN;

    readVersion();
}

void IOModule::setOutput(uint8_t outputId, uint8_t outputState)
{
    Serial2.write("!W");
    Serial2.write(outputId);
    Serial2.write(outputState);
    Serial2.write(';');

//#if DEBUG_TO_UART
    Serial.print("setOutput: ");
    Serial.print(outputId);
    Serial.print(" to ");
    Serial.println(outputState);
//#endif

    unsigned long startMillis = millis();
    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
            if (rx == '#')
            {
                // Done
                if(outputState == 1) {
                    outputPorts |= 1 << outputId;
                } else {
                    outputPorts &= ~(1 << outputId);
                }

                return;
            }
        }
    }
}

void IOModule::toggleOutput(uint8_t outputId) {
    uint8_t state = (outputPorts >> outputId) & 1;
    setOutput(outputId, state);
}

uint8_t IOModule::readInputs()
{
    //Clear the input buffer
    while (Serial2.available())
    {   
        Serial2.read();
    }

    Serial2.write("!R;"); // Start !, Read command, END ;
    Serial2.flush();

    //The response is 
    //#!FF;
    //OK_BYTE #
    //START_BYTE !
    //2 DATA BYTES
    //END BYTE ;

    char rxBuffer[10];
    char rxBufferSize = 0;
    unsigned long startMillis = millis();

    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
            //Serial.print(rx, HEX);
            rxBuffer[rxBufferSize++] = rx;

            //Check for end byte
            if(rxBuffer[rxBufferSize-1] == ';') {
                //Validate the data
                if(rxBuffer[rxBufferSize-5] == '#' && rxBuffer[rxBufferSize-4] == '!') {
                    inputPorts = (uint16_t) ((rxBuffer[rxBufferSize - 3] << 6) | rxBuffer[rxBufferSize - 2]) >> 2;
                    return 1;
                }
            }
        }
    }

    return 0;
}

void IOModule::readVersion()
{
    Serial2.write("!V;"); // Start !, Version command, END ;

    char rxBuffer[10];
    char rxBufferSize = 0;
    unsigned long startMillis = millis();

    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
            rxBuffer[rxBufferSize++] = rx;

            if (rxBufferSize > 1 && rxBuffer[rxBufferSize - 2] == '#')
            {
                // We have 2 or more chars in the buffer and the last 2 are OK command + 1 data byte
                // Serial.print(rxBuffer[rxBufferSize - 2]);
                // Serial.print(rxBuffer[rxBufferSize - 1]);

                picVersion = rxBuffer[rxBufferSize-1];
                return;
            }
        }
    }
}


void IOModule::setDefaults() {
    for(uint8_t i = 0; i < 5; i++) {
        setChannelType(i, DeviceSettings::getInstance().getOutputType(i));
    }

    setFadeInSpeed(DeviceSettings::getInstance().getOutputFadeInSpeed());
    setFadeOutSpeed(DeviceSettings::getInstance().getOutputFadeInSpeed());
    setDebounceTime(DeviceSettings::getInstance().getOutputDebounceTime());
}

void IOModule::setDimmer(uint8_t outputId, uint16_t outputState) {
    Serial2.flush();
    Serial2.write("!D");
    Serial2.write(outputId);

    if(outputState > DIMMER_MAX_STEP) {
        outputState = DIMMER_MAX_STEP;
    }

    lastDimValue[outputId] = outputState;

    //Shift output state by 1 bit so it would not collide with the END BYTE
    outputState = outputState << 1;
    uint8_t byte1 = outputState >> 8;
    uint8_t byte2 = outputState;

    Serial2.write(byte1);
    Serial2.write(byte2);
    Serial2.write(';');

    unsigned long startMillis = millis();
    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
        }
    }
}

/*
void IOModule::setFadeInSpeed(uint8_t outputId, uint8_t speed) {
    Serial2.flush();
    Serial2.write("!1");
    Serial2.write(outputId);

    //Prevent protocol collisions
    //speed == END_BYTE ';' 
    if(speed == 59) {
        speed++;
    }

    Serial2.write(speed);
    Serial2.write(';');

    unsigned long startMillis = millis();
    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
        }
    }
}

void IOModule::setFadeOutSpeed(uint8_t outputId, uint8_t speed) {
    Serial2.flush();
    Serial2.write("!2");
    Serial2.write(outputId);

    //Prevent protocol collisions
    //speed == END_BYTE ';' 
    if(speed == 59) {
        speed++;
    }

    Serial2.write(speed);
    Serial2.write(';');

    unsigned long startMillis = millis();
    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
        }
    }
}
*/

void IOModule::setFadeInSpeed(uint8_t speed) {
    Serial2.flush();
    Serial2.write("!1");

    //Prevent protocol collisions
    //speed == END_BYTE ';' 
    if(speed == 59) {
        speed++;
    }

    Serial2.write(speed);
    Serial2.write(';');

    unsigned long startMillis = millis();
    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
        }
    }
}

void IOModule::setFadeOutSpeed(uint8_t speed) {
    Serial2.flush();
    Serial2.write("!2");
    
    //Prevent protocol collisions
    //speed == END_BYTE ';' 
    if(speed == 59) {
        speed++;
    }

    Serial2.write(speed);
    Serial2.write(';');

    unsigned long startMillis = millis();
    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
        }
    }
}

void IOModule::setChannelType(uint8_t outputId, uint8_t type) {
    Serial2.flush();
    Serial2.write("!T");
    Serial2.write(outputId);

    if(type > 1) {
        type = 1;
    }

    Serial2.write(type);
    Serial2.write(';');

    unsigned long startMillis = millis();
    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
        }
    }
}

void IOModule::setDebounceTime(uint16_t time) {
    Serial2.flush();
    Serial2.write("!3");

    //Shift output state by 1 bit so it would not collide with the END BYTE
    time = time << 1;
    uint8_t byte1 = time >> 8;
    uint8_t byte2 = time;

    Serial2.write(byte1);
    Serial2.write(byte2);
    Serial2.write(';');

    unsigned long startMillis = millis();
    while (millis() - startMillis < 100)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
        }
    }
}



uint16_t IOModule::getDimmer(uint8_t outputId) {
    return lastDimValue[outputId];
}

uint8_t IOModule::checkInputs()
{
    // There is a change
    if (digitalRead(_interrupt_pin) == 1)
    {
        oldInputPorts = inputPorts;
        if(readInputs() && oldInputPorts != inputPorts) {
            return 1;
        }
        //Serial.print("Inputs: ");
        //Serial.println(inputPorts, BIN);
    }

    return 0;
}


void IOModule::debug()
{
    Serial2.write("!X;"); // Start !, Debug command, END ;

    //Receive the debug info
    unsigned long startMillis = millis();
    while (millis() - startMillis < 1000)
    {
        if (Serial2.available() > 0)
        {
            // Data received
            char rx = Serial2.read();
            Serial.write(rx);
            startMillis = millis();
        }
    }

}

void IOModule::reboot()
{
    Serial2.write("!Q;"); // Start !, Reboot command, END ;

    //Pic will return OK command
}