#ifndef CONFIG_H
#define CONFIG_H

#define DEBUG_TO_UART 1

#define APP_SOFTWARE_VERSION 1

#define NUM_INPUTS      12
#define NUM_OUTPUTS     9

#define MAX_TEMPERATURE 70

#define LAN_RESET 13
#define LAN_INTERRUPT 5

//LAN_CS is used in the Ethernet lib by the name PIN_SPI_SS_ETHERNET_LIB
#define PIN_SPI_SS_ETHERNET_LIB 4

#define CAN_RX_PIN GPIO_NUM_21
#define CAN_TX_PIN GPIO_NUM_22

#define PIC_RX_PIN 16
#define PIC_TX_PIN 17
#define PIC_INTERRUPT_PIN 25

#define PIC_MCLR 27
#define PIC_PGD 26
#define PIC_PGC 32

#define STATUS_LED 15

#define ONE_WIRE_BUS 33


#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)

#endif