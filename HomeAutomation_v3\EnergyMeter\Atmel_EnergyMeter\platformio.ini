; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:AVR128DB48]
platform = atmelmegaavr
board = AVR128DB48
framework = arduino

platform_packages =             ; Compiles without this set?
  ; DxCore AVD-DB
  platformio/framework-arduino-megaavr-dxcore@^1.5.6
  
board_build.f_cpu = 24000000L   ; 24 MHz
#board_hardware.oscillator = internal

#board_hardware.uart = uart3     ; May not be required? 
monitor_speed = 115200

#lib_deps =
  #https://github.com/openenergymonitor/RFM69_LPL
  #https://github.com/openenergymonitor/emonLibDB
  #https://github.com/openenergymonitor/emonEProm#avrdb
  #https://github.com/greiman/SSD1306Ascii

#[env:Upload_USART]
#build_flags = -DUSING_OPTIBOOT  ; Upload via uart
#upload_protocol = arduino
#upload_speed = 115200
#upload_flags = -v               ; Verbose