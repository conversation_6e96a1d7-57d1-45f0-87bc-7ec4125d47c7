; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:stable]
;platform = https://github.com/pioarduino/platform-espressif32/releases/download/stable/platform-espressif32.zip
platform = platformio/espressif32  @ ^6.10.0
board = esp32dev
framework = arduino


monitor_speed = 115200

lib_deps = 
    https://github.com/dawi<PERSON><PERSON><PERSON><PERSON><PERSON>/arduino-home-assistant.git
    https://github.com/PaulStoffregen/OneWire.git
    https://github.com/milesburton/Arduino-Temperature-Control-Library.git


;Original CAN library, but we need the 1.1.2 version because the 2.0.0 uses Arduino 3.0 core, that platform.io is not suporting yet
;https://github.com/pierremolinaro/acan-esp32.git
    
; Partition table for 4mb flash
;https://esp32.jgarrettcorbin.com/
;board_build.partitions = partitions.csv

build_flags = -DCORE_DEBUG_LEVEL=1 -I include

;upload_protocol = custom
;upload_command = python.exe espota.py -i ************* -p 80 -f .pio/build/esp32dev/firmware.bin

;Files in DATA dir can be uploaded to the SPIFFS, but its not an easy task to do and we have to do two separate OTA updates - 1 for code and 1 for static files
;That's why we have the pre_build.py
;The script is getting index.html, gzip it and put the bytes into a header file , that is later inclued in the webserver
extra_scripts = pre:pre_build.py
