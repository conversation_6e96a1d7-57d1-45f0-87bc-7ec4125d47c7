#include "canManager.h"

CANManager* CANManager::instance = nullptr;

bool CANManager::begin(gpio_num_t rxPin, gpio_num_t txPin) {
    if (initialized) return true;

    // Get device ID from last byte of MAC address
    const byte* mac = DeviceSettings::getInstance().getDeviceMACAddress();
    deviceId = mac[5];

    // Configure CAN bus
    ACAN_ESP32_Settings settings(500000); // 500 kbit/s
    settings.mRequestedCANMode = ACAN_ESP32_Settings::NormalMode;
    settings.mRxPin = rxPin;
    settings.mTxPin = txPin;

    const uint32_t errorCode = ACAN_ESP32::can.begin(settings);
    initialized = (errorCode == 0);
    
    return initialized;
}

void CANManager::update() {
    if (!initialized) return;

    CANMessage frame;
    while (ACAN_ESP32::can.receive(frame)) {
        handleMessage(frame);
    }
}

void CANManager::handleMessage(const CANMessage& frame) {
    uint8_t targetDeviceId = CANProtocol::getDeviceIdFromCanId(frame.id);
    
    // Ignore messages not meant for this device
    if (targetDeviceId != deviceId && targetDeviceId != 0xFF) return;

    CANProtocol::Command cmd = CANProtocol::getCommandFromCanId(frame.id);
    uint8_t portNumber = frame.data[1];
    uint8_t value = frame.data[2];
    uint8_t sourceDeviceId = frame.data[3];

    switch (cmd) {
        case CANProtocol::GET_INPUT_STATE: {
            bool state = IOModule::getInstance().getInput(portNumber);
            sendInputStateResponse(sourceDeviceId, portNumber, state);
            break;
        }
        case CANProtocol::SET_OUTPUT_STATE: {
            IOModule::getInstance().setOutput(portNumber, value);
            sendOutputStateResponse(sourceDeviceId, portNumber, value);
            break;
        }
        case CANProtocol::INPUT_STATE_RESPONSE:
        case CANProtocol::OUTPUT_STATE_RESPONSE:
        case CANProtocol::HEARTBEAT:
            // Handle responses if needed
            break;
    }
}

bool CANManager::requestInputState(uint8_t targetDeviceId, uint8_t inputNumber) {
    CANMessage frame;
    frame.id = CANProtocol::createCanId(targetDeviceId, CANProtocol::GET_INPUT_STATE);
    frame.len = 4;
    frame.data[0] = CANProtocol::GET_INPUT_STATE;
    frame.data[1] = inputNumber;
    frame.data[2] = 0;
    frame.data[3] = deviceId; // Source device ID
    
    return ACAN_ESP32::can.tryToSend(frame);
}

bool CANManager::setOutputState(uint8_t targetDeviceId, uint8_t outputNumber, bool state) {
    CANMessage frame;
    frame.id = CANProtocol::createCanId(targetDeviceId, CANProtocol::SET_OUTPUT_STATE);
    frame.len = 4;
    frame.data[0] = CANProtocol::SET_OUTPUT_STATE;
    frame.data[1] = outputNumber;
    frame.data[2] = state ? 1 : 0;
    frame.data[3] = deviceId; // Source device ID
    
    return ACAN_ESP32::can.tryToSend(frame);
}

bool CANManager::sendInputStateResponse(uint8_t targetDeviceId, uint8_t inputNumber, bool state) {
    CANMessage frame;
    frame.id = CANProtocol::createCanId(targetDeviceId, CANProtocol::INPUT_STATE_RESPONSE);
    frame.len = 4;
    frame.data[0] = CANProtocol::INPUT_STATE_RESPONSE;
    frame.data[1] = inputNumber;
    frame.data[2] = state ? 1 : 0;
    frame.data[3] = deviceId; // Source device ID
    
    return ACAN_ESP32::can.tryToSend(frame);
}

bool CANManager::sendOutputStateResponse(uint8_t targetDeviceId, uint8_t outputNumber, bool state) {
    CANMessage frame;
    frame.id = CANProtocol::createCanId(targetDeviceId, CANProtocol::OUTPUT_STATE_RESPONSE);
    frame.len = 4;
    frame.data[0] = CANProtocol::OUTPUT_STATE_RESPONSE;
    frame.data[1] = outputNumber;
    frame.data[2] = state ? 1 : 0;
    frame.data[3] = deviceId; // Source device ID
    
    return ACAN_ESP32::can.tryToSend(frame);
}

bool CANManager::sendHeartbeat() {
    CANMessage frame;
    frame.id = CANProtocol::createCanId(0xFF, CANProtocol::HEARTBEAT); // Broadcast
    frame.len = 4;
    frame.data[0] = CANProtocol::HEARTBEAT;
    frame.data[1] = 0;
    frame.data[2] = 0;
    frame.data[3] = deviceId; // Source device ID
    
    return ACAN_ESP32::can.tryToSend(frame);
}