#ifndef IOModule_lib
#define IOModule_lib

#include <Arduino.h>
#include "global_config.h"
#include "deviceSettings.h"

class IOModule
{
public:
    // Singleton instance
    static IOModule& getInstance();
    
    void begin(uint8_t RX_PIN, uint8_t TX_PIN, uint8_t INTERRUPT_PIN);
    
    uint8_t readInputs();
    uint8_t checkInputs();

    void readVersion();
    void toggleOutput(uint8_t outputId);

    void setDefaults();
    void setOutput(uint8_t outputId, uint8_t outputState);
    void setDebounceTime(uint16_t time);
    void debug();
    void reboot();

    
    uint8_t picVersion = 0;

    uint16_t inputPorts;
    uint16_t oldInputPorts;

    uint16_t outputPorts;

private:
    // Private constructor to enforce singleton pattern
    IOModule();

    //Used to determin the hold down on input buttons
    unsigned long lastButtonStateChange[12] = {0,0,0,0,0,0,0,0,0,0,0,0};
    
    

    uint8_t _interrupt_pin;
};

#endif